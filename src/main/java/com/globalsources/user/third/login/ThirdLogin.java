package com.globalsources.user.third.login;

import com.globalsources.user.api.dto.ThirdExtParam;
import com.globalsources.user.api.vo.ThirdLoginVO;

public interface ThirdLogin {
    /**
     * 获取第三方登录信息
     * @param accessToken
     * @return
     */
    ThirdLoginVO.ThirdInfo loadThirdUserInfo(String accessToken, ThirdExtParam extParam);

    /**
     * load user info from third platform
     * @param idToken
     * @param extParam
     * @return return userInfo
     */
    ThirdLoginVO.ThirdInfo loadThirdUserInfoForApp(String idToken, ThirdExtParam extParam);
}
