package com.globalsources.user.core.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> <PERSON>
 * @date 2021/7/26
 */
@ApiModel(description = "SourcingClubSummary")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("sc_summary")
public class SourcingClubSummaryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "", required = false)
    @TableId(value = "buyer_id", type = IdType.INPUT)
    private Long buyerId;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "first_name")
    private String firstName;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "last_name")
    private String lastName;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "email_addr")
    private String emailAddr;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "admin_point")
    private Long adminPoint;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "redeem_point")
    private Long redeemPoint;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "metric_point")
    private Long metricPoint;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "daily_admin_point")
    private Long dailyAdminPoint;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "total_point")
    private Long totalPoint;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "blacklist_flag")
    private String blacklistFlag;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "mc_spam_blacklist_flag")
    private String mcSpamBlacklistFlag;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "prev_week_add_point")
    private Long prevWeekAddPoint;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "prev_week_redeem_point")
    private Long prevWeekRedeemPoint;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "prev_week_l_upd_date")
    private Date prevWeekLUpdDate;

    @ApiModelProperty(value = "", required = false)
    @Column(name = "l_upd_date")
    private Date lUpdDate;
}
