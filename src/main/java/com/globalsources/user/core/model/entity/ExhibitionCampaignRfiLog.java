package com.globalsources.user.core.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ExhibitionCampaignRfiLog对象", description="")
public class ExhibitionCampaignRfiLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ecrl_id", type = IdType.AUTO)
    private Long ecrlId;

    private Long exhCampaignId;

    private Long userId;

    private String rfiId;

    private String rfiStatus;

    private Date rfiSendDate;

    private Date rfiApproveDate;

    private Long exhCampaignTaskId;

    private Date createDate;

    private Date lUpdDate;


}
