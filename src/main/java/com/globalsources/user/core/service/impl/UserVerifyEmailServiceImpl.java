package com.globalsources.user.core.service.impl;

import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.user.core.api.dto.DoiStatisDTO;
import com.globalsources.user.core.api.model.po.UserVerifyEmail;
import com.globalsources.user.core.api.vo.DoiStatisVO;
import com.globalsources.user.core.api.vo.UserVerifyEmailVO;
import com.globalsources.user.core.dao.UserVerifyEmailMapper;
import com.globalsources.user.core.model.sso.SsoProfileInfo;
import com.globalsources.user.core.service.SsoUserInfoService;
import com.globalsources.user.core.service.UserVerifyEmailService;
import com.globalsources.user.core.utils.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class UserVerifyEmailServiceImpl implements UserVerifyEmailService {
    @Resource
    private SsoUtil ssoUtil;
    @Resource
    private SsoUserInfoService ssoUserInfoService;
    @Resource
    private UserVerifyEmailMapper userVerifyEmailMapper;

    @Override
    public Result<Void> add(UserVerifyEmail dto) {
        try {
            userVerifyEmailMapper.insert(dto);
            return Result.success();
        }catch (Exception e){
            log.error("insert user verify email data occur exception, dto:{},detail:{}",dto,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> update(UserVerifyEmail dto) {
        userVerifyEmailMapper.updateById(dto);
        return Result.success();
    }

    @Override
    public Result<UserVerifyEmail> getById(Long userId) {
        UserVerifyEmail data=userVerifyEmailMapper.selectById(userId);
        return Result.success(data);
    }

    @Override
    public Result<UserVerifyEmailVO> getVerifyEmailInfo(Long userId) {
        SsoProfileInfo.PexUser user=ssoUserInfoService.getUserById(userId);
        if(user==null){
            return Result.failed(ResultCode.UserResultCode.USER_NOT_FOUND);
        }

        UserVerifyEmailVO data=new UserVerifyEmailVO();

        //获取LDAP doi时间
        String emailStatusDate=user.getEmailStatusDate();
        Date verifyDate=ssoUtil.parseSsoDate(emailStatusDate);
        data.setVerifyDate(verifyDate);

        //查询doi渠道信息
        UserVerifyEmail userVerifyEmail=userVerifyEmailMapper.selectById(userId);
        if(userVerifyEmail==null){
            return Result.success(data);
        }

        data.setSourceCode(userVerifyEmail.getSourceCode());
        data.setRemark(userVerifyEmail.getRemark());
        return Result.success(data);
    }

    @Override
    public Result<List<DoiStatisVO>> statisByFromCode(DoiStatisDTO dto) {
        try {
            List<DoiStatisVO> list = userVerifyEmailMapper.statisByFromCode(dto.getStartDate(),dto.getEndDate());
            return Result.success(list);
        }catch (Exception e){
            log.error("query user verify email statistics data occur exception, dto:{}, detail:{}",dto,e.getMessage(),e);
            return Result.error();
        }
    }
}
