package com.globalsources.user.core.controller;

import com.globalsources.framework.result.Result;
import com.globalsources.user.core.api.model.entity.UserAuthApp;
import com.globalsources.user.core.service.UserAuthAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "用户授权app")
@RequestMapping("/user/auth/app")
@RestController
public class UserAuthAppController {
    @Resource
    private UserAuthAppService userAuthAppService;

    @ApiOperation("查询授权应用信息")
    @PostMapping("/info")
    public Result<UserAuthApp> getUserAuthInfo(@RequestParam Long userId, @RequestParam String name){
        return userAuthAppService.queryUserAuthInfo(userId,name);
    }


    @ApiOperation("批量查询授权应用信息")
    @PostMapping("/info/batch/query")
    public Result<List<UserAuthApp>> batchQueryUserAuthApp(@RequestBody List<Long> userIds, @RequestParam String name){
        return userAuthAppService.batchQueryUserAuthApp(userIds,name);
    }


    @ApiOperation("批量添加授权应用")
    @PostMapping("/batch/add")
    public Result<Void> batchAddUserAuthApp(@RequestBody List<UserAuthApp> list){
        return userAuthAppService.batchAddUserAuthApp(list);
    }
}
