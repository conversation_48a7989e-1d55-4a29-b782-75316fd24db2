package com.globalsources.user.controller;

import com.globalsources.user.api.vo.SourcingClubSummaryVO;
import com.globalsources.user.service.SourcingClubSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/7/29
 */
@Slf4j
@RestController
@RequestMapping("/sc-summary")
@Api(tags = {"用户总积分接口"})
public class SourcingClubSummaryController {
    @Autowired
    private SourcingClubSummaryService sourcingClubSummaryService;

    @ApiOperation(value = "根据邮箱查找积分用户", notes = "根据邮箱查找积分用户")
    @GetMapping(value = "v1/qeury-email/{emailAddr}")
    public SourcingClubSummaryVO getSourcingClubSummaryByEmail(@PathVariable String emailAddr) {
        return sourcingClubSummaryService.getSourcingClubSummaryByEmail(emailAddr);
    }

    @ApiOperation(value = "根据用户ID查找积分用户", notes = "根据用户ID查找积分用户")
    @GetMapping(value = "v1/qeury-id/{buyerId}")
    public SourcingClubSummaryVO getSourcingClubSummaryById(@PathVariable Long buyerId) {
        return sourcingClubSummaryService.getSourcingClubSummaryById(buyerId);
    }
}
