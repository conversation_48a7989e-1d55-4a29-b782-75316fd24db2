package com.globalsources.message.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.globalsources.message.dto.EmailRecordRfiDeliveryDTO;
import com.globalsources.message.po.EmailRecord;
import com.globalsources.message.vo.EmailRecordRfiDeliveryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/21
 */
@Mapper
public interface EmailRecordMapper extends BaseMapper<EmailRecord> {

    List<EmailRecord> queryEmailRecordList(@Param("list") List<EmailRecord> list);

    int updateEmailRecordList(@Param("list") List<EmailRecord> list);

    int deleteEmailRecordList(@Param("list") List<EmailRecord> list);

    IPage<EmailRecordRfiDeliveryVO> queryRfiDeliveryList(IPage<EmailRecordRfiDeliveryDTO> page, @Param("dto")EmailRecordRfiDeliveryDTO emailRecordRfiDeliveryDTO);
}
