package com.globalsources.message.mq;

import com.alibaba.fastjson.JSON;
import com.globalsources.message.service.BuyerPushPlanService;
import com.globalsources.user.api.vo.UserEventVO;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@RabbitListener(queues = {"user.event"},ackMode = "AUTO")
public class UserMsgListener {
    @Resource
    private BuyerPushPlanService buyerPushPlanService;
    /**
     * 接收用户登录消息
     */
    @RabbitHandler
    public void receiveUserEventMsg(String msg, Channel channel, Message message) throws IOException {
        String json=new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("receive user event msg: json:{}",json);
        if(StringUtils.isEmpty(json)){
            log.warn("receive user event msg is empty");
            return;
        }

        UserEventVO userEvent= JSON.parseObject(json,UserEventVO.class);
        buyerPushPlanService.addPushPlanFromUserEvent(userEvent);
    }
}
