package com.globalsources.database.transport.dao.pgsql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.database.api.model.dto.UserRspCalcDTO;
import com.globalsources.database.api.model.vo.UserRspCalcVO;
import com.globalsources.database.transport.model.po.UserReqResponsePO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 供应商用户回复率计算 Mapper
 * @since 2021/8/10
 */
@Mapper
public interface UserReqResponseMapper extends BaseMapper<UserReqResponsePO> {

    /**
     * 主账号回复数据
     * @param userRspCalcDTO UserRspCalcDTO
     * @return UserRspCalcVO
     */
    UserReqResponsePO getMainAccountRspData(UserRspCalcDTO userRspCalcDTO);

}
