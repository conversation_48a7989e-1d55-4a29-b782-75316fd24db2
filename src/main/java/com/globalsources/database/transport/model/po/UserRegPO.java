package com.globalsources.database.transport.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("user_reg_snapshot")
public class UserRegPO {
    @ApiModelProperty(value = "用户唯一标识符,这个字段并不参与序列化")
    @JsonIgnore
    @TableField(exist = false)
    private String distinctId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "平台类型")
    private String platformType;

    @TableField(value = "event_date")
    private Date addDate;

    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "l_upd_date")
    private Date lUpdDate;
}
