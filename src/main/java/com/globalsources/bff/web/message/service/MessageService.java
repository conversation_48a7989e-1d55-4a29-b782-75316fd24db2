package com.globalsources.bff.web.message.service;

import com.globalsources.framework.vo.InquiryNoticeVO;
import com.globalsources.message.dto.OrderNoticeDTO;

import java.util.List;

/**
 * <AUTHOR> liu
 * @date 2021/7/19
 */
public interface MessageService {

    List<InquiryNoticeVO> notice(Long userId, Long supplierId, Integer roleType, String language, String platform);

    int readNotice(Long id, Integer roleType);

    int cleanAll(Long userId, Long supplierId, Integer roleType);

    InquiryNoticeVO newNoticeOne(Long userId, Long supplierId, Integer roleType, String language, String platform);

    int sendOrderMessage(OrderNoticeDTO orderNoticeDTO);
}
