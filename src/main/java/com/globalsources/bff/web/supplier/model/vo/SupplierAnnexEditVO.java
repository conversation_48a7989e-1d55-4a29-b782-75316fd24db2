package com.globalsources.bff.web.supplier.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/3/15 9:35
 */
@ApiModel(description = "SupplierAnnex")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SupplierAnnexEditVO  implements Serializable {

    private static final long serialVersionUID = 5315122487424134381L;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", required = false)
    @Size(max = 60, message = "{range}")
    private String name;

    /**
     * url
     */
    @ApiModelProperty(value = "url", required = false)
    private String url;

    /**
     * 大小,单位：kb
     */
    @ApiModelProperty(value = "大小,单位：kb", required = false)
    private Double size;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容", required = false)
    private String context;


    @ApiModelProperty("大图url")
    private String bigImageUrl;

    @ApiModelProperty("小图url")
    private String smallImageUrl;

}