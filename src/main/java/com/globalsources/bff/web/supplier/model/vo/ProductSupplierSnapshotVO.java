package com.globalsources.bff.web.supplier.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/3/26 14:04
 */
@ApiModel(description = "产品详情页公司公司主要信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductSupplierSnapshotVO {

    @ApiModelProperty(value = "公司id", required = false)
    private Long supplierId;

    @ApiModelProperty(value = "公司名", required = false)
    private String companyName;

    @ApiModelProperty("供应商的最大合同等级,0-1为P0-P6,-1不显示")
    private Integer maxContractLevel;

    @ApiModelProperty("新合同类型, AGG: P0050, 旧P0-P6: P0100/P1100/P2100/P3400/P4100/P5100/P6100, 新P3-P6(标准 NEWPSD, 高级 NEWPAD, 超级 NEWPSP, 至尊 NEWPVP): P3200/P4500/P5500/P6500")
    private String contractCode;
    @ApiModelProperty("合同对应 group code，具体值看DB数据")
    private String contractGroupCode;
    @ApiModelProperty("会员类型编号, agg:500,p0:1000,basic会员:2000,标准会员:3000,高级会员:4000,超级会员:5000,至尊会员:6000")
    private Integer memberTypeNum;

    @ApiModelProperty(value = "o2o图标flag")
    private Boolean o2oFlag;

    @ApiModelProperty(value = "合作年限")
    private Integer memberSince;

    @ApiModelProperty(value = "是否为已认证供应商")
    private Boolean verifiedSupplierFlag;

    @ApiModelProperty(value = "是否为已认证制造商")
    private Boolean verifiedManufacturerFlag;
    
    @ApiModelProperty(value = "供应商类型，包括：ADV/AGG/FL")
    private String supplierType;


    @ApiModelProperty("rfi平均回复时间评级，枚举，1:<=24h, 2:24-28h, 3:48-72h, 4:>72h")
    private Integer avgResponseTimeRatingEnum;

    /**
     * rfi回复率评级
     * - High
     * - Medium
     */
    @ApiModelProperty("rfi回复率评级")
    private String responseRateRating;

    @ApiModelProperty(value = "国家", required = false)
    private String country;

    @ApiModelProperty(value = "业务类型（key）", required = false)
    private String businessTypes;

    @ApiModelProperty("是否关注/收藏当前供应商")
    private Boolean collectFlag;

    @ApiModelProperty("VR图标flag")
    private Boolean vrFlag;

    @ApiModelProperty(value = "complete tour vr url")
    private String completeTourVrUrl;

    @ApiModelProperty(value = "vr cover url")
    private String vrCoverUrl;

    @ApiModelProperty("supplier short name, 域名自定义部分")
    private String supplierShortName;

    @ApiModelProperty("将来展会信息")
    private String futureTradeShowInfo;

    @ApiModelProperty("过去展会数量")
    private Integer pastShowCount;


//     Boolean exhibitorFlag

    @ApiModelProperty("是否运行中p0合同")
    private Boolean hasRunningP0;

}
