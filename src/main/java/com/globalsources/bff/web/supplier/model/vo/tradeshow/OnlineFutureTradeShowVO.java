package com.globalsources.bff.web.supplier.model.vo.tradeshow;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/06/11 17:00
 */
@ApiModel(description = "OnlineFutureTradeShowVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OnlineFutureTradeShowVO {

    @ApiModelProperty(value = "GS展会信息", required = false)
    private List<OnlineTradeShowVO> gsTradeShows;

    @ApiModelProperty(value = "第三方展会信息", required = false)
    private List<OnlineTradeShowVO> thirdTradeShows;

    @ApiModelProperty("聊天按钮在线状态， 不显示‘invalid’，在线‘Online’or'PushOnline' ,离线 ‘Offline’")
    private String chatOnlineStatus;
}
