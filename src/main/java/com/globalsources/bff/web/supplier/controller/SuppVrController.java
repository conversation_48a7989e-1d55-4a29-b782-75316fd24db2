package com.globalsources.bff.web.supplier.controller;

import com.globalsources.bff.web.supplier.model.vo.vr.SuppVrSearchVO;
import com.globalsources.bff.web.supplier.service.SuppVrService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @date 2022/1/13 8:34
 */
@Api(tags = "supplier供应商服务")
@RequestMapping("/supplier")
@RestController()
public class SuppVrController {

    @Autowired
    private SuppVrService suppVrService;

    @Login(validLogin = false)
    @ApiOperation(value = "查询供应商VR列表")
    @GetMapping("/v1/vr/list")
    public Result<PageResult<SuppVrSearchVO>> getSuppVrList(@ApiIgnore UserVO user, @RequestParam("pageNum") Long pageNum, @RequestParam("pageSize") Long pageSize){
        PageResult<SuppVrSearchVO> vrList = suppVrService.getVrList(user, pageNum, pageSize);
        return Result.success(vrList);
    }
}
