package com.globalsources.bff.web.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/8/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ApiModel(value = "alert supplier List", description = "AlertSupplierListReqDTO")
public class AlertSupplierListReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("页数  default 1")
    private Integer pageNum = 1;

    @ApiModelProperty("每页条数 default 10")
    private Integer pageSize = 10;

    @ApiModelProperty("邮件中的用户token")
    private String token;
}
