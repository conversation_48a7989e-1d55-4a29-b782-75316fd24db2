package com.globalsources.bff.web.vo.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/11/26 21:24
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BuyerSecuritySettingBffVO implements Serializable {
    private static final long serialVersionUID = 7817009394860944570L;

    private Boolean toptActiveFlag;

    private Boolean smsTfaActiveFlag;

    private Boolean smsTfaExpireTipFlag;

}
