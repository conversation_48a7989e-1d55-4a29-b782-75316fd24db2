package com.globalsources.bff.web.livestream.config;

import com.globalsources.activity.agg.api.vo.LivePlanVO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "ts")
public class ActivityConfig {
    private List<LivePlanVO> livePlan;
}
