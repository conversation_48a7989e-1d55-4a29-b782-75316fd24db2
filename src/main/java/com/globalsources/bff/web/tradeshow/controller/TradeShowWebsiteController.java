package com.globalsources.bff.web.tradeshow.controller;

import com.globalsources.bff.web.tradeshow.service.TradeShowProductService;
import com.globalsources.bff.web.tradeshow.service.TradeShowService;
import com.globalsources.bff.web.tradeshow.service.TradeShowSupplierService;
import com.globalsources.bff.web.tradeshow.vo.TSProductDetailVO;
import com.globalsources.bff.web.tradeshow.vo.TradeShowProductVO;
import com.globalsources.bff.web.tradeshow.vo.TradeShowSupplierVO;
import com.globalsources.bff.web.tradeshow.vo.TsPavilionVO;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfi.agg.response.email.TradeQuestionVO;
import com.globalsources.ts.api.model.vo.desktop.TsContactGlobalSourcesVO;
import com.globalsources.ts.api.model.vo.desktop.TsPressInquiryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: create by Corin Chen
 * @version: v1.0
 * @description: com.globalsources.bff.web.tradeshow.controller
 * @date:2021/7/20
 */
@RequestMapping("/trade-show")
@RestController
@Slf4j
@Api(tags = {"提供给SITE TEAM的trade show 相关接口"})
public class TradeShowWebsiteController {

    @Autowired
    private TradeShowProductService tradeShowProductService;

    @Autowired
    private TradeShowService tradeShowService;

    @Autowired
    private TradeShowSupplierService tradeShowSupplierService;

    @Login(validLogin = false)
    @GetMapping(value = "v1/detail/{id}")
    @ApiOperation(value = "tradeshow 商品详情接口")
    public Result<TSProductDetailVO> productDetail(@ApiIgnore UserVO userVO, @PathVariable Long id) {
        TSProductDetailVO desktopDetailVo = tradeShowProductService.productDetail(id);
        return Result.success(desktopDetailVo);
    }

    @PostMapping(value = "v1/list-product")
    @ApiOperation(value = "根据产品Id 批量查询产品")
    public Result<List<TradeShowProductVO>> queryProductByIds(@RequestBody List<Long> productIds) {
        List<TradeShowProductVO> result = tradeShowProductService.queryProductByIds(productIds);
        return Result.success(result);
    }

    @ApiOperation(value = "根据展会ID获取Pavilion列表", notes = "根据展会ID获取Pavilion列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "tsIds", value = "tsIds"),
            @ApiImplicitParam(paramType = "query", name = "pageIndex", value = "Page Index", dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "Page Size", dataType = "int")
    })
    @PostMapping("/v1/list-ts-pavilion")
    public Result<PageResult<TsPavilionVO>> getTsPavilionListByTsIds(@RequestParam List<Long> tsIds,
                                                                     @RequestParam(value = "pageIndex", required = false, defaultValue = "1") Integer pageIndex,
                                                                     @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize) {
        return tradeShowService.getTsPavilionListByTsIds(tsIds, pageIndex, pageSize);
    }

    @Login(validLogin = false)
    @GetMapping(value = "v1/recommend-product")
    @ApiOperation(value = "tradeshow 推荐商品接口")
    public Result<List<TradeShowProductVO>> recommendProduct(@ApiIgnore UserVO userVO, Long categoryId,@RequestHeader(required = false,value = "AnonymousId")String anonymousId) {
        List<TradeShowProductVO> recommendProduct = tradeShowProductService.recommendProduct(userVO, categoryId,anonymousId);
        return Result.success(recommendProduct);
    }

    @PostMapping(value = "v1/supplier-infos")
    @ApiOperation(value = "根据供应商id， 批量查询供应商信息")
    public Result<List<TradeShowSupplierVO>> querySupplierInfoById(@RequestBody List<Long> supplierIds) {
        List<TradeShowSupplierVO> supplierInfoByIds = tradeShowSupplierService.getSupplierInfoForTsWebsiteByIds(supplierIds);
        return Result.success(supplierInfoByIds);
    }

    @PostMapping(value = "v1/press-inquiry")
    @ApiOperation(value = "press inquiry")
    public Result<String> pressInquiry(@RequestBody @Valid TsPressInquiryVO tradeShowPressInquiryVO) {
        return tradeShowService.pressInquiry(tradeShowPressInquiryVO);
    }

    @PostMapping(value = "v1/contact-global-sources")
    @ApiOperation(value = "contact global sources")
    public Result<String> contactGlobalSources(@RequestBody @Valid TsContactGlobalSourcesVO tradeShowContactGlobalSourcesVO) {
        return tradeShowService.contactGlobalSources(tradeShowContactGlobalSourcesVO);
    }

    @PostMapping(value = "v1/submit-trade-question")
    @ApiOperation(value = "submit trade question")
    public Result<String> submitTradeQuestion(@RequestBody TradeQuestionVO tradeQuestionVO) {
        return tradeShowService.submitTradeQuestion(tradeQuestionVO);
    }
}
