package com.globalsources.bff.web.campaign.controller;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.operation.agg.api.feign.CampaignCommentFeign;
import com.globalsources.operation.agg.api.vo.campaigncomment.CampaignCommentItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Api(tags = "Campaign-Comment")
@RestController
@RequestMapping("/campaign-comment")
public class CampaignCommentController {

    @Autowired
    private CampaignCommentFeign campaignCommentFeign;


    @ApiOperation(value = "get campaign comment item by comment id", notes = "get campaign comment item by comment id")
    @GetMapping("/v1/get-campaign-comment-item-list-by-comment-id")
    public Result<PageResult<CampaignCommentItemVO>> getCampaignCommentItemListByCommentId(@RequestParam(defaultValue = "1") Long pageNum,
                                                                                           @RequestParam(defaultValue = "6") Long pageSize,
                                                                                           @RequestParam Integer campaignCommentId) {
        return campaignCommentFeign.getCampaignCommentItemListByCommentId(pageNum, pageSize, campaignCommentId);
    }

}
