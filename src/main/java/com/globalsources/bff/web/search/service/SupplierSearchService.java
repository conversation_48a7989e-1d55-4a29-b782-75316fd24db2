package com.globalsources.bff.web.search.service;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.search.api.dto.SupplierSearchDTO;
import com.globalsources.search.api.result.SupplierSearchResult;
import com.globalsources.search.api.result.free.FreeSupplierVO;
import com.globalsources.search.api.vo.supplier.RecommendedSupplierVo;
import com.globalsources.search.api.vo.supplier.SupplierSuggestVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Title: SupplierSearchService
 * @Author: Johann
 * @Description: SupplierSearchService
 * @date 2021/07/13 - 10:37
 */
public interface SupplierSearchService {

    SupplierSearchResult supplierSearch(SupplierSearchDTO search);

    SupplierSearchResult changeToSupplier(SupplierSearchDTO changeSupplier);

    Result<List<SupplierSuggestVo>> supplierSuggest(String query,String language);

    Result<PageResult<RecommendedSupplierVo>> getRecommendedSuppliers(String String, Integer pageNum, Integer pageSize);

    List<RecommendedSupplierVo> getRecSuppliersForCountryPage(@RequestParam("countryCode") String countryCode);

    Result<PageResult<FreeSupplierVO>> freeSupplierSearch(SupplierSearchDTO search);
}
