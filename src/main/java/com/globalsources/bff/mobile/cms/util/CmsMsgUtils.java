package com.globalsources.bff.mobile.cms.util;

import com.globalsources.bff.mobile.cms.response.Msg;
import com.globalsources.framework.result.Result;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2022/6/17 11:31
 */
public class CmsMsgUtils {
    private CmsMsgUtils() {
    }

    public static <T> Result<T> convertMsgToResult(Msg msg, Class<T> clazz) {
//        Result result = convertMsgToResult(msg)    data: [] mapping失败 无法序列化
        Result<T> result = new Result<>();
        BeanUtils.copyProperties(msg, result);
        return result;
    }
}
