package com.globalsources.bff.mobile.ts.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TsRegConfirmVO {
    @ApiModelProperty("名字")
    private String firstName;
    @ApiModelProperty("姓氏")
    private String lastName;
    @ApiModelProperty("公司名字")
    private String companyName;
    @ApiModelProperty("展会列表")
    private List<TsInfo> tsList;
    @ApiModelProperty("确认码")
    private Long confirmId;
    private String visitorId;
    private String countryName;
    @ApiModelProperty("展会列表")
    private String barCodeImg;
    private String buyerType;
    private String source;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;
    private Long regBatchId;

    @Data
    public static class TsInfo{
        @ApiModelProperty("展会id")
        private Long tsId;
        @ApiModelProperty("展会名字")
        private String name;
        @ApiModelProperty("展会组code")
        private String tsGrpCode;
        @ApiModelProperty("开始时间")
        private Date startDate;
        @ApiModelProperty("结束时间")
        private Date endDate;
    }
}
