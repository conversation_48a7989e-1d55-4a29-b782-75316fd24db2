package com.globalsources.bff.mobile.ts.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class APPTsIdUserRegV2DTO {

    @ApiModelProperty("推荐id")
    private String referralId;

    @ApiModelProperty("来源渠道")
    @Size(max = 100, message = "UTM length cannot exceed 100 characters")
    private String utm;

    @ApiModelProperty("展会id列表")
    private List<Long> tradeShowIds;

    /*Name */
    @ApiModelProperty("Name:Title")
    @NotBlank(message = "Title is required. Please enter information")
    @Size(max = 6, message = "Title length cannot exceed 6 characters")
    private String title;

    /*Registration Type*/
    @ApiModelProperty("buyer type")
    private String buyerType;

    @ApiModelProperty("Name:FirstName")
    @NotBlank(message = "First name is required. Please enter information")
    @Size(max = 35, message = "First name length cannot exceed 35 characters")
    private String firstName;

    @ApiModelProperty("Name:LastName")
    @NotBlank(message = "Last name is required. Please enter information")
    @Size(max = 35, message = "Last name length cannot exceed 35 characters")
    private String lastName;

    /*Business Email */
    @ApiModelProperty("Business Email")
    @NotBlank(message = "Mailbox cannot be null")
    @Email(message = "The mailbox format is incorrect")
    @Size(max = 100, message = "mailbox length cannot exceed 100 characters")
    private String email;

    /*Country/Region */
    @ApiModelProperty("Country/Region")
    @NotBlank(message = "Country code is required. Please enter information")
    @Size(max = 6, message = "Country code length cannot exceed 6 characters")
    private String countryCode;

    /*Job Title*/
    @ApiModelProperty("Job Title")
    @NotBlank(message = "Job Title is required. Please enter information")
    @Size(max = 35, message = "Job Title length cannot exceed 35 characters")
    private String jobTitle;

    /*Job Function */
    @ApiModelProperty("Job Function")
    @NotBlank(message = "Job function is required. Please enter information")
    @Size(max = 50, message = "Job function length cannot exceed 50 characters")
    private String jobFunction;

    @ApiModelProperty("Custom Job Function")
    @Size(max = 70, message = "Custom Job function length cannot exceed 70 characters")
    private String customJobFunction;

    /*Job Level */
    @ApiModelProperty("Job Level")
    @Size(max = 50, message = "Job level length cannot exceed 50 characters")
    private String jobLevel;

    /*Mobile Phone Number */
    @ApiModelProperty("Mobile country")
    @NotBlank(message = "Mobile country is required. Please enter information")
    @Size(max = 6, message = "Mobile country length cannot exceed 4 characters")
    private String mobileCountry;

    @ApiModelProperty("Mobile number")
    @NotBlank(message = "Mobile number is required. Please enter information")
    @Size(max = 20, message = "Mobile number length cannot exceed 20 characters")
    private String mobileNumber;

    /*Business Address */
    @NotBlank(message = "Unit/Floor/Building/Street is required. Please enter information")
    @ApiModelProperty("Unit/Floor/Building/Street")
    @Size(max = 35, message = "Address1 length cannot exceed 35 characters")
    private String address1;

    @ApiModelProperty("City/Town")
    @Size(max = 35, message = "City/Town length cannot exceed 35 characters")
    private String address2;

    @ApiModelProperty("Province/State")
    @Size(max = 35, message = "Province/State length cannot exceed 35 characters")
    private String address3;

    @ApiModelProperty("Zip Code")
    @Size(max = 15, message = "Zip Code length cannot exceed 15 characters")
    private String postCode;

    /*Company Websites */
    @ApiModelProperty("Company Websites")
    @Size(max = 60, message = "Company website url length cannot exceed 60 characters")
    private String companyWebsiteURL;

    /*Company Name */
    @ApiModelProperty("Company Name")
    @NotBlank(message = "Company name is required. Please enter information")
    @Size(max = 100, message = "Company name length cannot exceed 100 characters")
    private String companyName;

    /*What is your company's business type? (please choose all that apply)*/
    @ApiModelProperty("What is your company's business type:businessType")
    @NotEmpty(message = "What is your company's business type:businessType is required. Please enter information")
    private List<String> businessTypes;

    @ApiModelProperty("What is your company's business type:customBusinessType")
    @Size(max = 70, message = "Custom business type length cannot exceed 70 characters")
    private String customBusinessType;

    @ApiModelProperty("What is your company's business type:onlineStoreUrl")
    private String onlineStoreUrl;

    /*What is the major business type of your company? (please choose the most suitable answer) */
    @ApiModelProperty("What is the major business type of your company? (please choose the most suitable answer)")
    @NotBlank(message = "What is the major business type of your company is required. Please enter information")
    private String businessType;

    /*Company Size*/
    @ApiModelProperty("Company Size")
    private String totalEmployee;

    /*What is the annual sourcing amount of your company? (USD)  */
    @ApiModelProperty("What is the annual sourcing amount of your company")
    private String annualSourcingValue;

    /*What is the annual sourcing amount of your business unit/department? */
    @ApiModelProperty("What is the annual sourcing amount of your business unit/department")
    private String annualSourcingAmountOfBusinessUnit;

    /*Your Selling Channel*/
    @ApiModelProperty("Your Selling Channel")
    private List<String> sellingChannels;

    @ApiModelProperty("语言")
    private String language;

    @ApiModelProperty("verification code")
    private Integer verificationCode;

    @ApiModelProperty("BusMatchService")
    private String busMatchService;

    @ApiModelProperty("SpecificTours")
    private String specificTours;

    @ApiModelProperty("展会产品Pref列表")
    @NotEmpty(message = "ProductPref list is required. Please enter information")
    private List<ProductPref> productPrefList;

    @Data
    public static class ProductPref{
        @ApiModelProperty("展会id")
        private Long tradeShowId;
        @ApiModelProperty("产品prof codeId")
        private String codeId;
        @ApiModelProperty("codeId所对应的名字")
        private String name;
    }
}
