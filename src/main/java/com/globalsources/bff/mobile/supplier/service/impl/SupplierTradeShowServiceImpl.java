package com.globalsources.bff.mobile.supplier.service.impl;

import com.globalsources.agg.supplier.api.feign.TradeshowAggFeign;
import com.globalsources.agg.supplier.api.model.dto.tradeshow.TradeShowDisplayDTO;
import com.globalsources.agg.supplier.api.model.dto.tradeshow.TradeShowHomeDTO;
import com.globalsources.bff.mobile.supplier.config.SuppTradeShowProperties;
import com.globalsources.bff.mobile.supplier.model.vo.tradeshow.AppFutureTradeShowVO;
import com.globalsources.bff.mobile.supplier.model.vo.tradeshow.MobileOnlineTradeShowVO;
import com.globalsources.bff.mobile.supplier.model.vo.tradeshow.SupplierTradeShowPageVO;
import com.globalsources.bff.mobile.supplier.service.SupplierTradeShowService;
import com.globalsources.bff.mobile.utils.LocalDateUtil;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Chen
 * @date 2021/9/6 20:55
 */
@Slf4j
@Service
public class SupplierTradeShowServiceImpl implements SupplierTradeShowService {

    public static final String VN = "VN";

    @Autowired
    private TradeshowAggFeign tradeshowAggFeign;

    @Autowired
    private SuppTradeShowProperties suppTradeShowProperties;

    @Override
    public SupplierTradeShowPageVO getSupplierTradeShowPageInfo(Long supplierId, UserVO userVO ) {
        SupplierTradeShowPageVO result = new SupplierTradeShowPageVO();
        result.setHasTradeShowFlag(Boolean.TRUE.equals(hasTradeShowInfo(supplierId)));
        AppFutureTradeShowVO futureTradeShows = getFutureTradeShows(supplierId, userVO);
        if (Objects.nonNull(futureTradeShows)) {
            List<MobileOnlineTradeShowVO> gsFutureTradeShows = futureTradeShows.getGsFutureTradeShows();
            result.setGsFutureTradeShows(gsFutureTradeShows);
            for (MobileOnlineTradeShowVO ts : gsFutureTradeShows) {
                String tsGrpCode = VN.equalsIgnoreCase(ts.getCountryCode()) ? VN : ts.getTsGrpCode();
                String tsImgUrl = Optional.ofNullable(suppTradeShowProperties).map(SuppTradeShowProperties::getTradeShowImgMap).map(map -> map.get(tsGrpCode)).orElse(StringUtils.EMPTY);
                ts.setTsImageUrl(tsImgUrl);
                ts.setListImage(Lists.newArrayList(tsImgUrl));
            }
            result.setThirdFutureTradeShows(futureTradeShows.getThirdFutureTradeShows());
        }
        result.setGsPastTradeShows(getPastGsTradeShows(supplierId));
        result.setThirdPastTradeShows(getPastThirdTradeShows(supplierId));
        return result;
    }

    @Override
    public AppFutureTradeShowVO getFutureTradeShows(Long supplierId, UserVO userVO) {
        AppFutureTradeShowVO result = null;
        Result<TradeShowHomeDTO> tradeshowHomeResult = tradeshowAggFeign.getFutureTradeShows(supplierId);
        TradeShowHomeDTO futureTradeShowDto = ResultUtil.getData(tradeshowHomeResult);
        if (Objects.nonNull(futureTradeShowDto)) {
            result = new AppFutureTradeShowVO();
            result.setGsFutureTradeShows(convertMobileOnlineTradeShowVos(futureTradeShowDto.getGsTradeShows()));
            result.setThirdFutureTradeShows(convertMobileOnlineTradeShowVos(futureTradeShowDto.getThirdTradeShows()));
        }
        return result;
    }

    @Override
    public List<MobileOnlineTradeShowVO> getPastGsTradeShows(Long supplierId) {
        Result<List<TradeShowDisplayDTO>> tradeshowResult = tradeshowAggFeign.getPastGsTradeShows(supplierId);
        List<TradeShowDisplayDTO> tradeshowDtos = ResultUtil.getData(tradeshowResult);
        return convertMobileOnlineTradeShowVos(tradeshowDtos);
    }

    @Override
    public List<MobileOnlineTradeShowVO> getPastThirdTradeShows(Long supplierId) {
        Result<List<TradeShowDisplayDTO>> tradeshowResult = tradeshowAggFeign.getPastThirdTradeShows(supplierId);
        List<TradeShowDisplayDTO> tradeshowDtos = ResultUtil.getData(tradeshowResult);
        return convertMobileOnlineTradeShowVos(tradeshowDtos);
    }

    private List<MobileOnlineTradeShowVO> convertMobileOnlineTradeShowVos(List<TradeShowDisplayDTO> tradeshowDtos) {
        List<MobileOnlineTradeShowVO> tradeshowList = OrikaMapperUtil.coverList(tradeshowDtos, MobileOnlineTradeShowVO.class);
        if (CollectionUtils.isNotEmpty(tradeshowList)) {
            tradeshowList.stream().filter(Objects::nonNull).forEach(tradeshow -> tradeshow.setShowDate(LocalDateUtil.spliceTradeShowDate(tradeshow.getTsStartDate(), tradeshow.getTsEndDate(), true)));
            tradeshowList.sort(Comparator.comparing(MobileOnlineTradeShowVO::getTsStartDate, Comparator.reverseOrder()));
        }
        return tradeshowList;
    }

    @Override
    public Boolean hasTradeShowInfo(Long supplierId) {
        Result<Boolean> result = tradeshowAggFeign.hasTradeShowInfo(supplierId);
        return ResultUtil.getData(result);
    }
}
