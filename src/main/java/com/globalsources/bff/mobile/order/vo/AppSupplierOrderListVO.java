package com.globalsources.bff.mobile.order.vo;

import com.globalsources.order.api.model.OrderProduct;
import com.globalsources.order.api.vo.OrderDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppSupplierOrderListVO {

    @ApiModelProperty("订单Id")
    private Long orderId;

    @ApiModelProperty("买家照片")
    private String buyerPhoto;

    @ApiModelProperty("买家名称")
    private String buyerName;

    @ApiModelProperty("买家国家")
    private String buyerCountry;

    @ApiModelProperty("订单状态")
    private String orderStatus;

    @ApiModelProperty("订单状态文本，用于页面显示")
    private String orderStatusCopy;

    @ApiModelProperty("产品信息列表")
    private List<OrderProduct> productVOList;

    @ApiModelProperty(value = "订单状态码")
    private Integer orderStatusCode;

    @ApiModelProperty(value = "总订单价格")
    private BigDecimal totOrderCost;

    @ApiModelProperty(value = "原始总订单")
    private BigDecimal totProductCostOriginal;

    @ApiModelProperty(value = "订单创建日期")
    private Long orderCreateDate;

    @ApiModelProperty(value = "供应商合同即将过期标识,true代表即将过期",required = false)
    private Boolean supplierExpire;

    @ApiModelProperty(value = "物流信息",required = false)
    private OrderDetailVO.ShippingInfoVO shippingInfoVO;

    @ApiModelProperty(value = "卖家端顶部文本")
    private String supplierTOPCopy;
}
