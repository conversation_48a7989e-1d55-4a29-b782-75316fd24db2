package com.globalsources.bff.mobile.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.globalsources.bff.mobile.coupon.vo.UserCoupon;
import com.globalsources.bff.mobile.order.dto.OrderSubmitDTO;
import com.globalsources.bff.mobile.order.service.BuyerOrderService;
import com.globalsources.bff.mobile.order.service.CommonService;
import com.globalsources.bff.mobile.order.vo.AppBuyerOrderDetailVO;
import com.globalsources.bff.mobile.order.vo.AppBuyerOrderListVO;
import com.globalsources.bff.mobile.order.vo.AppGradientPriceVO;
import com.globalsources.bff.mobile.order.vo.AppOrderMaxAmount;
import com.globalsources.bff.mobile.order.vo.AppOrderProductInfoVO;
import com.globalsources.bff.mobile.order.vo.AppOrderTrackingVO;
import com.globalsources.bff.mobile.order.vo.AppPlaceOrderVO;
import com.globalsources.bff.mobile.order.vo.AppProductInfoVO;
import com.globalsources.bff.mobile.order.vo.OrderSubmitVO;
import com.globalsources.bff.mobile.order.vo.ShippingAddressVO;
import com.globalsources.coupon.core.api.feign.CouponCoreFeign;
import com.globalsources.coupon.core.api.po.CouponConfigPO;
import com.globalsources.coupon.core.api.po.CouponExpandPO;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.order.api.dto.BuyerListQueryDTO;
import com.globalsources.order.api.dto.PlaceOrderDTO;
import com.globalsources.order.api.dto.ShipmentInfoDTO;
import com.globalsources.order.api.dto.cart.BuyerRefundDTO;
import com.globalsources.order.api.enums.DirectOrderStatus;
import com.globalsources.order.api.feign.BuyerOrderAggFeign;
import com.globalsources.order.api.model.DOProduct;
import com.globalsources.order.api.model.DOShipment;
import com.globalsources.order.api.vo.BuyerDetailVO;
import com.globalsources.order.api.vo.BuyerListProductVO;
import com.globalsources.order.api.vo.BuyerListVO;
import com.globalsources.order.api.vo.DOProductTrackingVO;
import com.globalsources.order.api.vo.DetailProductVO;
import com.globalsources.order.api.vo.OperationRecordVO;
import com.globalsources.order.api.vo.OrderTrackingVO;
import com.globalsources.order.api.vo.PaymentInfoVO;
import com.globalsources.order.api.vo.PlaceOrderVO;
import com.globalsources.order.api.vo.PreOrderProduct;
import com.globalsources.order.api.vo.PreOrderVO;
import com.globalsources.order.api.vo.ShippingInfoVO;
import com.globalsources.order.core.api.feign.OrderCoreFeign;
import com.globalsources.order.core.api.order.po.DirectOrderShippingDetailPO;
import com.globalsources.user.api.feign.UserQueryFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class BuyerOrderServiceImpl implements BuyerOrderService {

    // private static final String RECEIVED_AND_ACTIVE = "RECEIVED-AND-ACTIVE";

    private static final String MAX_AMOUNT_PER_ORDER = "10000";

    private static final String MAX_PRODUCT_AMOUNT_PER_ORDER = "8000";

    @Autowired
    private BuyerOrderAggFeign buyerOrderAggFeign;

    @Autowired
    private OrderCoreFeign orderCoreFeign;

    @Autowired
    private CommonService commonService;

    @Autowired
    private UserQueryFeign userQueryFeign;

    @Autowired
    private CouponCoreFeign couponCoreFeign;

    @Override
    public Result<AppPlaceOrderVO> getOrderInitInfo(List<Long> productIdList, List<Integer> quantityList, Long userId) {

        PreOrderVO preOrderVO = buyerOrderAggFeign.preOrder(productIdList, quantityList, userId, null).getData();
        if (ObjectUtil.isNull(preOrderVO)) {
            return Result.success();
        }

        // product
        List<PreOrderProduct> preProductList = preOrderVO.getProductList();
        List<AppOrderProductInfoVO> appOrderProductInfoVOS = preProductList.stream().map(e -> {
            AppOrderProductInfoVO productInfo = new AppOrderProductInfoVO();
            productInfo.setQty(String.valueOf(e.getQty()));
            AppProductInfoVO appProductInfoVO = new AppProductInfoVO();
            appProductInfoVO.setProductId(e.getProductId());
            appProductInfoVO.setSupplierId(preOrderVO.getSupplierId());
            appProductInfoVO.setProductName(e.getProductName());
            appProductInfoVO.setModelNumber(e.getModelNumber());
            appProductInfoVO.setMinOrder(String.valueOf(e.getMinOrderQty()));
            appProductInfoVO.setProductImage(e.getProductImageUrl());
            appProductInfoVO.setMinOrderUnit(e.getMinOrderUnit());
            appProductInfoVO.setCategoryId(e.getCategoryId());
            productInfo.setProductInfo(appProductInfoVO);
            productInfo.setGradientPrices(new ArrayList<>());
            String productUnit = e.getMinOrderUnit();
            List<Map<String, BigDecimal>> priceMap = e.getPriceMap();

            List<String> quantityRangeList = CollUtil.newArrayList();
            List<String> priceList = CollUtil.newArrayList();
            priceMap.forEach(map -> {
                quantityRangeList.add(String.valueOf(map.get("quantity")));
                priceList.add(String.valueOf(map.get("price")));
            });
            for (int i = 0; i < quantityRangeList.size(); i++) {

                String maxQuantity;
                if ((i + 1) == quantityRangeList.size()) {
                    maxQuantity = String.valueOf(99999999);
                } else {
                    Long quantity = Long.parseLong(quantityRangeList.get(i + 1)) - 1;
                    maxQuantity = String.valueOf(quantity);
                }

                productInfo.getGradientPrices().add(new AppGradientPriceVO(quantityRangeList.get(i), maxQuantity, priceList.get(i), productUnit));
            }

            return productInfo;
        }).collect(Collectors.toList());

        // 收获地址
        ShippingAddressVO shippingAddress = null;
        com.globalsources.order.api.vo.ShippingAddressVO shippingAddressVO = preOrderVO.getShippingAddressVO();
        if (ObjectUtil.isNotNull(shippingAddressVO)) {
            shippingAddress = OrikaMapperUtil.coverObject(preOrderVO.getShippingAddressVO(), ShippingAddressVO.class);
            shippingAddress.setAddr_1(shippingAddressVO.getAddr1());
            shippingAddress.setAddr_2(shippingAddressVO.getAddr2());
        }

        // tracking
        AppOrderTrackingVO trackingVO = commonService.assembleAppOrderTrackingVO(preOrderVO.getOrderTrackingVO());

        // user coupon list
        OrderTrackingVO orderTrackingVO = preOrderVO.getOrderTrackingVO();
        List<DOProductTrackingVO> productTrackingVOList = orderTrackingVO.getProductTrackingVOList();
        // 获取下单产品的L4列表并去重
        List<Long> categoryIds = productTrackingVOList.stream().map(DOProductTrackingVO::getL4CategoryId).distinct().collect(Collectors.toList());
        log.info(" product categoryIds:{}", JSON.toJSONString(categoryIds));

        List<UserCoupon> userCoupons = new ArrayList<>();
        // 用户未登录也可以进入预下单页
        Optional.ofNullable(userId).ifPresent(buyerId -> {
            List<CouponExpandPO> couponList = couponCoreFeign.selectReceivedAndAvailableCouponByUserId(userId).getData();
            if (!CollectionUtils.isEmpty(couponList)) {
                couponList.forEach(couponExpandPO -> {
                    UserCoupon userCoupon = new UserCoupon();
                    userCoupon.setCouponId(couponExpandPO.getCouponId());
                    userCoupon.setAmount(BigDecimal.valueOf(couponExpandPO.getAmount()));
                    userCoupon.setThresholdAmount(BigDecimal.valueOf(couponExpandPO.getThresholdAmount()));
                    List<Long> canNotUseProductIdList = getCanNotUseProductIdList(couponExpandPO.getCouponId(), categoryIds, productTrackingVOList);
                    userCoupon.setProductCanNotUseList(canNotUseProductIdList);
                    if (!CollectionUtils.isEmpty(canNotUseProductIdList)) {
                        userCoupon.setProductCanNotUseFlag(true);
                    }
                    userCoupons.add(userCoupon);
                });
            }
        });
        AppPlaceOrderVO appPlaceOrderVO = AppPlaceOrderVO.builder().productList(appOrderProductInfoVOS).shippingAddress(shippingAddress).maxOrderPrice(String.valueOf(preOrderVO.getMaxProductAmountPerOrder()))
                .supplierUserId(0L).supplierId(preOrderVO.getSupplierId()).companyName(preOrderVO.getSupplierName()).userCouponList(userCoupons)
                .trackingVO(trackingVO).build();

        return Result.success(appPlaceOrderVO);
    }

    private List<Long> getCanNotUseProductIdList(Integer couponId, List<Long> categoryIds, List<DOProductTrackingVO> productTrackingVOList) {
        List<Long> productIds = new ArrayList<>();

        CouponConfigPO couponConfigPO = CouponConfigPO.builder().couponId(couponId).build();
        List<CouponConfigPO> couponConfigPOList = couponCoreFeign.selectCouponConfig(couponConfigPO).getData();
        log.info(" coupon id :{} couponConfigPOList :{}", couponId, JSON.toJSONString(couponConfigPOList));
        if (!CollectionUtils.isEmpty(couponConfigPOList)) {
            String status = couponConfigPOList.get(0).getStatus();
            // 如果是仅部分可用
            if ("PartiallyAvailable".equals(status)) {
                return partiallyAvailable(categoryIds, couponConfigPOList, productTrackingVOList);
            } else {// 如果是部分不可用
                return partiallyUnAvailable(categoryIds, couponConfigPOList, productTrackingVOList);
            }
        }
        return productIds;
    }

    private List<Long> partiallyUnAvailable(List<Long> categoryIds, List<CouponConfigPO> couponConfigPOList, List<DOProductTrackingVO> productTrackingVOList) {
        List<Long> entityIdVOS = couponConfigPOList.stream().map(CouponConfigPO::getEntityId).collect(Collectors.toList());
        List<Long> productIds = new ArrayList<>();
        for (Long categoryId : categoryIds) {
            if (entityIdVOS.contains(categoryId)) { // entityIdVOS 部分不可用
                productTrackingVOList.forEach(productTrackingVO -> {
                    if (productTrackingVO.getL4CategoryId().equals(categoryId)) { // 遍历l4判断 如果是不可用的L4则把该L4产品加到不可用产品id集合
                        productIds.add(productTrackingVO.getProductId());
                    }
                });
            }
        }
        return productIds;
    }

    private List<Long> partiallyAvailable(List<Long> categoryIds, List<CouponConfigPO> couponConfigPOList, List<DOProductTrackingVO> productTrackingVOList) {
        List<Long> entityIdVOS = couponConfigPOList.stream().map(CouponConfigPO::getEntityId).collect(Collectors.toList());
        List<Long> productIds = new ArrayList<>();
        for (Long categoryId : categoryIds) {
            if (!entityIdVOS.contains(categoryId)) { // entityIdVOS 配置了可以的券的L4的类别
                productTrackingVOList.forEach(productTrackingVO -> {
                    if (productTrackingVO.getL4CategoryId().equals(categoryId)) { // 遍历l4判断 如果不是可用的L4则把该L4产品加到不可用产品id集合
                        productIds.add(productTrackingVO.getProductId());
                    }
                });
            }
        }
        return productIds;
    }

    @Override
    public Result<OrderSubmitVO> submitOrder(Long userId, OrderSubmitDTO orderSubmit) {

        PlaceOrderDTO placeOrderDTO = new PlaceOrderDTO();
        placeOrderDTO.setUserId(userId);
        placeOrderDTO.setSupplierId(Long.parseLong(orderSubmit.getSupplierId()));
        placeOrderDTO.setOrderSource("APP");
        placeOrderDTO.setRemark(orderSubmit.getRemark());
        placeOrderDTO.setUserCouponId(orderSubmit.getUserCouponId());
        String[] productIdString = StrUtil.split(orderSubmit.getProductIds(), ",");
        Long[] productIds = Arrays.stream(productIdString).map(Long::parseLong).toArray(Long[]::new);
        placeOrderDTO.setProductIds(productIds);
        String[] qtyString = StrUtil.split(orderSubmit.getQuantities(), ",");
        Integer[] qtys = Arrays.stream(qtyString).map(Integer::parseInt).toArray(Integer[]::new);
        placeOrderDTO.setQtys(qtys);

        DOShipment doShipment = JSON.parseObject(orderSubmit.getShippingAddressJson(), DOShipment.class);
        ShipmentInfoDTO shipmentInfoDTO = OrikaMapperUtil.coverObject(doShipment, ShipmentInfoDTO.class);
        shipmentInfoDTO.setAddr1(doShipment.getAddr_1());
        shipmentInfoDTO.setAddr2(doShipment.getAddr_2());
        shipmentInfoDTO.setAddr3(doShipment.getAddr_3());

        placeOrderDTO.setShipmentInfoDTO(shipmentInfoDTO);
        placeOrderDTO.setShoppingCartFlag(orderSubmit.getShoppingCartFlag());

        Result<PlaceOrderVO> result = buyerOrderAggFeign.placeOrder(placeOrderDTO);

        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        PlaceOrderVO placeOrderVO = result.getData();

        OrderSubmitVO orderSubmitVO = new OrderSubmitVO();
        orderSubmitVO.setFlag(true);
        orderSubmitVO.setOrderId(Long.valueOf(placeOrderVO.getOrderId()));
        // 如果有使用优惠券 返回使用成功的优惠券ID
        orderSubmitVO.setCouponId(placeOrderVO.getCouponId());
        return Result.success(orderSubmitVO);
    }

    @Override
    public Result<ShippingAddressVO> getOrderShippingAddress(Long userId) {
        ShippingAddressVO shippingAddressVO = null;
        DirectOrderShippingDetailPO directOrderShippingDetailPO = orderCoreFeign.queryUserLastShippingAddress(userId).getData();
        if (ObjectUtil.isNotNull(directOrderShippingDetailPO)) {
            shippingAddressVO = OrikaMapperUtil.coverObject(directOrderShippingDetailPO, ShippingAddressVO.class);
            shippingAddressVO.setAddr_1(directOrderShippingDetailPO.getAddr1());
            shippingAddressVO.setAddr_2(directOrderShippingDetailPO.getAddr2());
            shippingAddressVO.setAddr_3(directOrderShippingDetailPO.getAddr3());
        }
        return Result.success(shippingAddressVO);

    }

    @Override
    public Result<PageResult<AppBuyerOrderListVO>> getOrderList(Integer pageNum, Integer pageSize, Long userId) {
        BuyerListQueryDTO buyerListQueryDTO = new BuyerListQueryDTO();
        buyerListQueryDTO.setPageNum(pageNum);
        buyerListQueryDTO.setPageSize(pageSize);
        PageResult<BuyerListVO> pageResult = buyerOrderAggFeign.list(buyerListQueryDTO, userId, LanguageDicEnum.EN_US.getValue()).getData();

        List<BuyerListVO> buyerListVOS = pageResult.getList();
        if (CollUtil.isEmpty(buyerListVOS)) {
            return Result.success(PageResult.restPage(pageResult, AppBuyerOrderListVO.class));
        }

        List<AppBuyerOrderListVO> appBuyerOrderList = CollUtil.newArrayList();

        buyerListVOS.forEach(orderInfo -> {
            // product
            List<DOProduct> doProductList = CollUtil.newArrayList();
            List<BuyerListProductVO> productVOList = orderInfo.getProductVOList();
            if (CollUtil.isNotEmpty(productVOList)) {
                for (BuyerListProductVO buyerListProductVO : productVOList) {
                    DOProduct doProduct = new DOProduct();
                    doProduct.setMoqUnit(buyerListProductVO.getUnit());
                    doProduct.setPrimaryImageUrl(buyerListProductVO.getProductImageUrl());
                    doProduct.setProdModelNum(buyerListProductVO.getModelNumber());
                    doProduct.setSuppName(orderInfo.getSupplierName());
                    doProduct.setProductId(buyerListProductVO.getProductId());
                    doProduct.setOrderId(Long.valueOf(orderInfo.getOrderId()));
                    doProduct.setSuppId(orderInfo.getSupplierId());
                    doProduct.setProdName(buyerListProductVO.getProductName());
                    doProduct.setFob(buyerListProductVO.getFob());
                    doProduct.setQuotedFobPrice(buyerListProductVO.getQuoteUpdateFob());
                    doProduct.setProductQty(BigDecimal.valueOf(buyerListProductVO.getQty()));
                    doProductList.add(doProduct);
                }
            }

            AppBuyerOrderListVO appBuyerOrderListVO = AppBuyerOrderListVO.builder().supplierId(orderInfo.getSupplierId()).orderId(Long.valueOf(orderInfo.getOrderId())).orderStatus(orderInfo.getStatusCode())
                    .currencyCode("US").orderCreateDate(orderInfo.getCreateDate()).createDate(DateUtil.format(new Date(orderInfo.getCreateDate()), "yyyy/MM/dd")).totalProductCost(orderInfo.getTotalProductPrice())
                    .discountCost(orderInfo.getDiscountCost()).totalOrderCost(orderInfo.getTotalOrderPrice()).submitOrderCost(orderInfo.getTotalOrderPriceOriginal())
                    .shippingCost(orderInfo.getShippingCost()).totalRefund(orderInfo.getTotalRefund()).orgName(orderInfo.getSupplierName())
                    .products(doProductList).build();
            appBuyerOrderList.add(appBuyerOrderListVO);
        });
        return Result.success(PageResult.restPage(pageResult, appBuyerOrderList));
    }

    @Override
    public Result<AppBuyerOrderDetailVO> getOrderDetail(Long orderId) {

        AppBuyerOrderDetailVO appBuyerOrderDetailVO = null;
        BuyerDetailVO buyerDetailVO = buyerOrderAggFeign.detail(String.valueOf(orderId), LanguageDicEnum.EN_US.getValue()).getData();
        if (ObjectUtil.isNotNull(buyerDetailVO)) {
            // product
            List<AppBuyerOrderDetailVO.OrderProductVO> orderProductVOS = getOrderProductVOS(buyerDetailVO.getProductList());

            // buyer info
            AppBuyerOrderDetailVO.BuyerAddressVO buyerAddressVO = getBuyerAddressVO(buyerDetailVO.getShippingAddressVO());

            // pay info
            AppBuyerOrderDetailVO.PayInfoVO payInfoVO = getPayInfoVO(buyerDetailVO.getPaymentInfoVO());

            // shipping info
            AppBuyerOrderDetailVO.ShippingInfoVO shippingInfoVO = getShippingInfoVO(buyerDetailVO.getShippingInfo(), buyerDetailVO.getShippingCost());

            // status list
            List<AppBuyerOrderDetailVO.OrderStatusVO> orderStatusVOList = getOrderStatusVOS(buyerDetailVO.getOperationRecord());

            // progress
            List<AppBuyerOrderDetailVO.OrderProgressVO> progressList = orderStatusVOList.stream().map(e -> new AppBuyerOrderDetailVO.OrderProgressVO(e.getStatusCode(), e.getStatusName(), Long.valueOf(e.getStatusDate()), true)).collect(Collectors.toList());
            IntStream.range(0, progressList.size()).filter(i ->
                            progressList.get(i).getStatusName().equals(DirectOrderStatus.QUOTE_REVISED.name())).
                    boxed().findFirst().map(i -> progressList.remove((int) i));
            // 如果包含退款拒绝和纠纷创建则保持原有进度
            if (refundReject(progressList) || disputeCreated(progressList)) {
                List<String> filterList = Arrays.asList(DirectOrderStatus.REFUND_ISSUE.name(), DirectOrderStatus.REFUND_REJECTED.name(), DirectOrderStatus.DISPUTE_CREATED.name(), DirectOrderStatus.DISPUTE_CLOSED.name());
                progressList.removeIf(x -> filterList.contains(x.getStatusName()));
                // 如果当前订单状态是纠纷
                if (DirectOrderStatus.DISPUTE_CREATED.getCode().equals(buyerDetailVO.getStatusCode())) {
                    if (!shipped(progressList)) {
                        buyerDetailVO.setStatusCode(DirectOrderStatus.PAID.getCode());
                    } else {
                        buyerDetailVO.setStatusCode(DirectOrderStatus.SHIPPED.getCode());
                    }
                }
            }
            if (!DirectOrderStatus.CANCELLED.getCode().equals(buyerDetailVO.getStatusCode()) && !DirectOrderStatus.COMPLETED.getCode().equals(buyerDetailVO.getStatusCode())
                    && !DirectOrderStatus.REFUND_ISSUE.getCode().equals(buyerDetailVO.getStatusCode())) {
                //下单->报价->支付->发货->完成
                List<AppBuyerOrderDetailVO.OrderProgressVO> appendProgress = new ArrayList<>();
                appendProgress.add(new AppBuyerOrderDetailVO.OrderProgressVO(DirectOrderStatus.QUOTED.getCode(), DirectOrderStatus.QUOTED.name(), null, false));
                appendProgress.add(new AppBuyerOrderDetailVO.OrderProgressVO(DirectOrderStatus.PAID.getCode(), DirectOrderStatus.PAID.name(), null, false));
                appendProgress.add(new AppBuyerOrderDetailVO.OrderProgressVO(DirectOrderStatus.SHIPPED.getCode(), DirectOrderStatus.SHIPPED.name(), null, false));
                appendProgress.add(new AppBuyerOrderDetailVO.OrderProgressVO(DirectOrderStatus.COMPLETED.getCode(), DirectOrderStatus.COMPLETED.name(), null, false));
                int i = 0;
                if (DirectOrderStatus.QUOTED.getCode().equals(buyerDetailVO.getStatusCode())) {
                    i = 1;
                }
                if (DirectOrderStatus.PAID.getCode().equals(buyerDetailVO.getStatusCode())) {
                    i = 2;
                }
                if (DirectOrderStatus.SHIPPED.getCode().equals(buyerDetailVO.getStatusCode())) {
                    i = 3;
                }
                if (DirectOrderStatus.COMPLETED.getCode().equals(buyerDetailVO.getStatusCode())) {
                    i = 4;
                }
                progressList.addAll(appendProgress.subList(i, appendProgress.size()));
            }

            // coupon
            AppBuyerOrderDetailVO.CouponVO couponVO = OrikaMapperUtil.coverObject(buyerDetailVO.getCouponVO(), AppBuyerOrderDetailVO.CouponVO.class);

            appBuyerOrderDetailVO = AppBuyerOrderDetailVO.builder().supplierId(buyerDetailVO.getSupplierId()).orderId(Long.valueOf(buyerDetailVO.getOrderId()))
                    .orderNum(Long.valueOf(buyerDetailVO.getOrderId())).supplierName(buyerDetailVO.getSupplierName()).orderStatus(buyerDetailVO.getStatusCode())
                    .cancelledCode(buyerDetailVO.getCancelCode()).orderTotalCost(buyerDetailVO.getTotalOrderPrice().doubleValue()).productTotalCost(buyerDetailVO.getTotalProductPrice().doubleValue())
                    .shippingCost(buyerDetailVO.getShippingCost().doubleValue()).discount(buyerDetailVO.getDiscountCost().doubleValue()).totalRefund(buyerDetailVO.getTotalRefundAmount()).currency("US")
                    .productList(orderProductVOS).buyerInfo(buyerAddressVO).payInfo(payInfoVO).buyerNote(buyerDetailVO.getPlaceOrderNote()).supplierNote(buyerDetailVO.getSupplierQuoteNote()).refundReason(buyerDetailVO.getRefundReason())
                    .rejectRefundReason(buyerDetailVO.getRejectRefundReason()).shippingInfo(shippingInfoVO).orderStatusList(orderStatusVOList).progressList(progressList).buyerCopy(buyerDetailVO.getStatusCopy())
                    .extensionShippingDays(buyerDetailVO.getExtensionShippingDays()).quotedFlag(buyerDetailVO.getQuotedFlag()).updateFobRepeat(buyerDetailVO.getQuotedReFlag()).chatOnlineStatus(buyerDetailVO.getChatOnlineStatus())
                    .trackingVO(commonService.assembleAppOrderTrackingVO(buyerDetailVO.getOrderTrackingVO())).supplierUserId(buyerDetailVO.getSupplierUserId()).couponVO(couponVO).build();

        }
        return Result.success(appBuyerOrderDetailVO);
    }

    private Boolean refundReject(List<AppBuyerOrderDetailVO.OrderProgressVO> orderStatusVOList) {
        return orderStatusVOList.stream().anyMatch(x -> x.getStatusName().equals(DirectOrderStatus.REFUND_REJECTED.name()));
    }

    private Boolean disputeCreated(List<AppBuyerOrderDetailVO.OrderProgressVO> orderStatusVOList) {
        return orderStatusVOList.stream().anyMatch(x -> x.getStatusName().equals(DirectOrderStatus.DISPUTE_CREATED.name()));
    }

    private Boolean shipped(List<AppBuyerOrderDetailVO.OrderProgressVO> orderStatusVOList) {
        return orderStatusVOList.stream().anyMatch(x -> x.getStatusName().equals(DirectOrderStatus.SHIPPED.name()));
    }

    public AppBuyerOrderDetailVO.PayInfoVO getPayInfoVO(PaymentInfoVO paymentInfoVO) {
        AppBuyerOrderDetailVO.PayInfoVO payInfoVO = null;
        if (ObjectUtil.isNotNull(paymentInfoVO)) {
            payInfoVO = new AppBuyerOrderDetailVO.PayInfoVO();
            payInfoVO.setPayMethod(paymentInfoVO.getPayMethod());
            payInfoVO.setPaymentStatus(paymentInfoVO.getPayStatus());
            payInfoVO.setTotalPaid(paymentInfoVO.getPayAmount().toString());
        }
        return payInfoVO;
    }

    public AppBuyerOrderDetailVO.ShippingInfoVO getShippingInfoVO(ShippingInfoVO shippingInfo, BigDecimal shippingCost) {
        AppBuyerOrderDetailVO.ShippingInfoVO shippingInfoVO = null;
        if (ObjectUtil.isNotNull(shippingInfo)) {
            shippingInfoVO = new AppBuyerOrderDetailVO.ShippingInfoVO();
            shippingInfoVO.setShippingCost(shippingCost != null ? shippingCost.doubleValue() : null);
            shippingInfoVO.setTrackingNum(shippingInfo.getTrackingNum());
            shippingInfoVO.setCarrier(shippingInfo.getCarrier());
        }
        return shippingInfoVO;
    }

    public List<AppBuyerOrderDetailVO.OrderStatusVO> getOrderStatusVOS(List<OperationRecordVO> operationRecord) {
        List<AppBuyerOrderDetailVO.OrderStatusVO> orderStatusVOList = CollUtil.newArrayList();
        for (OperationRecordVO operationRecordVO : operationRecord) {
            AppBuyerOrderDetailVO.OrderStatusVO orderStatusVO = new AppBuyerOrderDetailVO.OrderStatusVO();
            orderStatusVO.setDateType(operationRecordVO.getKey());
            orderStatusVO.setStatusDate(operationRecordVO.getValue().toString());
            orderStatusVO.setStatusCode(operationRecordVO.getOrderStatus().getCode());
            orderStatusVO.setStatusName(operationRecordVO.getOrderStatus().name());
            orderStatusVOList.add(orderStatusVO);
        }
        return orderStatusVOList;
    }

    public List<AppBuyerOrderDetailVO.OrderProductVO> getOrderProductVOS(List<DetailProductVO> productList) {
        List<AppBuyerOrderDetailVO.OrderProductVO> orderProductVOS = CollUtil.newArrayList();
        for (DetailProductVO detailProductVO : productList) {
            AppBuyerOrderDetailVO.OrderProductVO orderProductVO = new AppBuyerOrderDetailVO.OrderProductVO();
            orderProductVO.setProductId(detailProductVO.getProductId());
            orderProductVO.setPrimaryImageUrl(detailProductVO.getProductImageUrl());
            orderProductVO.setName(detailProductVO.getProductName());
            orderProductVO.setModelNumber(detailProductVO.getModelNumber());
            orderProductVO.setQuotedFob(detailProductVO.getQuoteUpdateFob() != null ? detailProductVO.getQuoteUpdateFob() : detailProductVO.getUnitPrice());
            orderProductVO.setQuantity(BigDecimal.valueOf(detailProductVO.getQty()));
            orderProductVO.setAmount(detailProductVO.getTotalProductPrice());
            orderProductVO.setUnit(detailProductVO.getUnit());
            orderProductVO.setFob(detailProductVO.getUnitPrice());
            orderProductVO.setUpdateFobFlag(detailProductVO.getQuoteUpdateFob() != null);
            orderProductVOS.add(orderProductVO);
        }
        return orderProductVOS;
    }

    public AppBuyerOrderDetailVO.BuyerAddressVO getBuyerAddressVO(com.globalsources.order.api.vo.ShippingAddressVO shippingAddressVO) {
        AppBuyerOrderDetailVO.BuyerAddressVO buyerAddressVO = OrikaMapperUtil.coverObject(shippingAddressVO, AppBuyerOrderDetailVO.BuyerAddressVO.class);
        UserVO userVO = userQueryFeign.getUserByUserEmail(shippingAddressVO.getBuyerEmailAddr()).getData();
        if (ObjectUtil.isNotNull(userVO)) {
            buyerAddressVO.setBuyerId(userVO.getUserId());
            buyerAddressVO.setFirstName(userVO.getFirstName());
            buyerAddressVO.setLastName(userVO.getLastName());
            buyerAddressVO.setProvince(shippingAddressVO.getStateDesc());
            buyerAddressVO.setCompany(shippingAddressVO.getBuyerCompanyName());
            buyerAddressVO.setEmail(shippingAddressVO.getBuyerEmailAddr());
            buyerAddressVO.setReceiver(shippingAddressVO.getRecipientName());
        }
        return buyerAddressVO;
    }

    @Override
    public AppOrderMaxAmount getProductAndOrderMaxAmount() {
        AppOrderMaxAmount appOrderMaxAmount = new AppOrderMaxAmount();
        appOrderMaxAmount.setMaxAmountPerOrder(MAX_AMOUNT_PER_ORDER);
        appOrderMaxAmount.setMaxProductAmountPerOrder(MAX_PRODUCT_AMOUNT_PER_ORDER);
        return appOrderMaxAmount;
    }

    @Override
    public Boolean applyForRefund(String orderId, String remark) {
        Result<Boolean> result = buyerOrderAggFeign.refund(BuyerRefundDTO.builder().orderId(orderId).remark(remark).build());
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return result.getData();
    }

}
