package com.globalsources.bff.mobile.rfq.controller;

import com.globalsources.bff.mobile.rfq.service.RfqQuotationService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfq.bff.api.model.dto.req.SupplierAppRfqQuotationCreateRequestDTO;
import com.globalsources.rfq.bff.api.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 * <a>Title: RfqQuotationController </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR>
 * @date 2021/7/16 16:18
 */
@Slf4j
@RestController
@RequestMapping("rfq/quotation")
@Api(tags = {"rfq报价相关接口"})
public class RfqQuotationController {

    @Autowired
    private RfqQuotationService rfqQuotationService;

    @Login
    @ApiOperation(value = "卖家rfq--报价", notes = "卖家rfq--报价", tags = {"rfq报价相关接口"})
    @PostMapping("v1/create-quotation")
    public Result<String> createQuotation(@ApiIgnore UserVO userVO, @Valid SupplierAppRfqQuotationCreateRequestDTO requestDTO) {
        return rfqQuotationService.createQuotation(requestDTO.toBuilder().userId(UserUtil.getUserId(userVO)).supplierId(UserUtil.getSupplierId(userVO)).email(UserUtil.getEmail(userVO)).build());
    }

}