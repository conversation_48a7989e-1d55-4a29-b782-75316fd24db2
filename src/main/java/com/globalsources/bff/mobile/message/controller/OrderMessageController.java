package com.globalsources.bff.mobile.message.controller;


import com.globalsources.bff.mobile.message.service.OrderMessageService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.LanguageUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.message.vo.OrderNoticeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR> Chen
 * @date 2021/09/06
 */
@Api(tags = {"App Order消息"})
@RestController
@Slf4j
@RequestMapping("/order-msg")
public class OrderMessageController {

    @Autowired
    private OrderMessageService orderMessageService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 已读 消息
     */
    @PostMapping(value = "/readNotice/{id}")
    @Login
    public Result<Boolean> readNotice(@PathVariable Long id) {
        log.info("Input param in readNotice, id = {}", id);
        try {
            orderMessageService.readNotice(id);
            return Result.success(Boolean.TRUE);
        } catch (Exception e) {
            log.error("readNotice error, ", e);
            return Result.failed(e.getMessage());
        }

    }

    @ApiOperation(value = "根据ID删除消息")
    @DeleteMapping(value = "delete/{id}")
    @Login
    public Result<Boolean> deleteById(@PathVariable Long id) {
        log.info("Input param in deleteById, id = {}", id);
        try {
            orderMessageService.deleteById(id);
            return Result.success(Boolean.TRUE);
        } catch (Exception e) {
            log.error("deleteById error, ", e);
            return Result.failed(e.getMessage());
        }
    }


    @Login
    @ApiOperation(value = "订单通知列表")
    @GetMapping(value = "order-notice-list")
    public Result<PageResult<OrderNoticeVO>> getOrderNoticeList(@ApiIgnore UserVO userVO, @RequestParam(required = false) Integer pageNum, @RequestParam(required = false) Integer pageSize) {
        log.info("Input param in get order notice list , userVO = {}, pageNum = {}, pageSize = {}", userVO, pageNum, pageSize);
        if (Objects.isNull(pageNum) || pageNum == 0) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize) || pageSize == 0) {
            pageSize = 10;
        }
        String headerLanguage = "";
        Long supplierId = null;
        if (userVO.getType() == 1) {
            // 买家
            headerLanguage = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        } else if (userVO.getType() == 2) {
            // 卖家
            headerLanguage = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.ZH_CN);
            supplierId = userVO.getCurrentSupplier().getSupplierId();
            if (supplierId == null) {
                return Result.failed("当前用户的 supplier id 为空！");
            }
        }
        String language = LanguageUtil.convertLanguageEnumValue(headerLanguage);
        PageResult<OrderNoticeVO> pageResult = orderMessageService.getOrderNoticeList(pageNum, pageSize, userVO.getUserId(), userVO.getType(), supplierId, language);
        return Result.success(pageResult);
    }
}
