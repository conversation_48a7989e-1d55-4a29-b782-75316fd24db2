package com.globalsources.bff.mobile.user.dto.favorite;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/18 15:24
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFavoriteCollectBffDTO implements Serializable {
    private static final long serialVersionUID = 8180882413727518478L;

    @ApiModelProperty("收藏的产品或者供应商id")
    private Long id;
    @ApiModelProperty("true收藏/false不收藏")
    private Boolean collectFlag;
    @NotBlank
    @ApiModelProperty("收藏类型(供应商:supplier/产品:product)")
    private String type;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty("是否通过手机扫码关注")
    private Boolean scanFlag;

}
