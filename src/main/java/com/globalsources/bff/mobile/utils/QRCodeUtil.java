package com.globalsources.bff.mobile.utils;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;

public class QRCodeUtil {
    private static QrConfig initQrConfig() {
        QrConfig config = new QrConfig(136, 136);
        config.setMargin(1);
        return config;
    }

    /**
     * 生成base64 二维码
     * @param content
     * @return
     */
    public static String generateAsBase64(String content) {
        return QrCodeUtil.generateAsBase64(content, initQrConfig(), "png");
    }
}