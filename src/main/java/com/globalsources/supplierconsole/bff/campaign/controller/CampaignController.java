package com.globalsources.supplierconsole.bff.campaign.controller;

import com.globalsources.framework.annotation.AutoIdempotent;
import com.globalsources.framework.annotation.SupplierLogin;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.LanguageUtil;
import com.globalsources.framework.vo.BaseSupplierVO;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.supplierconsole.agg.api.annotation.IdParam;
import com.globalsources.supplierconsole.agg.api.annotation.IdParamEnabled;
import com.globalsources.supplierconsole.agg.api.campaign.dto.CampaignAggDTO;
import com.globalsources.supplierconsole.agg.api.campaign.dto.CampaignQueryAggDTO;
import com.globalsources.supplierconsole.agg.api.campaign.feign.CampaignAggFeign;
import com.globalsources.supplierconsole.agg.api.campaign.request.CampaignProductGroupLinkAggQueryDTO;
import com.globalsources.supplierconsole.agg.api.campaign.vo.CampaignSupplierProductLinkAggVO;
import com.globalsources.supplierconsole.agg.api.campaign.vo.ScCampaignAggVO;
import com.globalsources.supplierconsole.agg.api.campaign.vo.ScCampaignSimpleListAggVO;
import com.globalsources.supplierconsole.bff.campaign.model.dto.ScCampaignSubmissionSettingsBffDTO;
import com.globalsources.supplierconsole.bff.campaign.service.CampaignService;
import com.globalsources.supplierconsole.bff.campaign.vo.ScCampaignBannerListVO;
import com.globalsources.supplierconsole.bff.utils.SuppUserUtil;
import com.globalsources.supplierconsole.core.api.campaign.model.dto.CampaignProductLinkBatchAddDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Api(tags = "新营销活动")
@RequestMapping("/campaign")
@RestController
public class CampaignController {

    @Autowired
    private CampaignService campaignService;

    @Autowired
    private CampaignAggFeign campaignAggfeign;

    @ApiOperation(value = "商家报名信息(所有活动+已报名)", notes = "商家报名信息(所有活动+已报名)")
    @SupplierLogin(validPermission = false)
    @PostMapping("/v1/get-campaign-registration-info")
    @IdParamEnabled
    public Result<PageResult<ScCampaignSimpleListAggVO>> getCampaignRegistrationInfo(@ApiIgnore HttpServletRequest request,
                                                                                @ApiIgnore UserVO userVo,
                                                                                @IdParam(orgId = "#query.supplierId") @ApiParam(value = "搜索条件") @RequestBody CampaignQueryAggDTO query){
        SuppUserUtil.checkCurrentOrgId(userVo, query.getSupplierId());
        String language = getLanguage(request);
        if("ALL".equals(query.getSearchType())) {
            return campaignService.selectCampaignForRegistration(language,query.getSupplierId(),query);
        }else if("REGIST".equals(query.getSearchType())){
            return campaignService.loadCampaignForRegistration(language, query);
        }
        return null;
    }

    @ApiOperation(value = "商家报名-活动信息", notes = "商家报名-活动信息")
    @SupplierLogin(validPermission = false)
    @GetMapping("/v1/get-campaign-registration")
    @IdParamEnabled
    public Result<ScCampaignAggVO> getCampaignForRegistration(@ApiIgnore HttpServletRequest request,
                                                       @ApiIgnore UserVO userVo,
                                                       @ApiParam("活动id") @RequestParam(name = "campaignId") Long campaignId,
                                                       @IdParam(orgId = "#orgId") @RequestParam Long orgId,
                                                       @ApiParam("是否通过验证") @RequestParam(name = "isPassed",required = false) Boolean isPassed){
        SuppUserUtil.checkCurrentOrgId(userVo, orgId);
        String language = getLanguage(request);
        return campaignService.getCampaignForRegistration(language,campaignId,orgId,isPassed);
    }

    @ApiOperation(value = "商家取消活动报名", notes = "商家取消活动报名")
    @SupplierLogin(validPermission = false)
    @GetMapping("/v1/cancel-campaign-registration")
    @IdParamEnabled
    public Result<Boolean> cancelCampaignRegistration(@ApiIgnore UserVO userVo,
                                               @ApiParam("活动id") @RequestParam(name = "campaignId") Long campaignId,
                                               @IdParam(orgId = "#orgId") @RequestParam Long orgId){
        SuppUserUtil.checkCurrentOrgId(userVo, orgId);
        Long userId = Optional.ofNullable(userVo).map(UserVO::getUserId).orElse(null);
        return campaignService.cancelCampaignRegistration(campaignId,orgId,userId);
    }

    @ApiOperation(value = "商家活动报名", notes = "商家活动报名")
    @SupplierLogin(validPermission = false)
    @PostMapping("/v1/campaign-registration")
    @IdParamEnabled
    public Result<Boolean> campaignRegistration(@ApiIgnore UserVO userVo,
                                         @IdParam(orgId = "#campaign.supplierId") @RequestBody CampaignAggDTO campaign){
        SuppUserUtil.checkCurrentOrgId(userVo, campaign.getSupplierId());
        Long userId = Optional.ofNullable(userVo).map(UserVO::getUserId).orElse(null);
        Date today = new Date();
        campaign.setCreatedBy(userId);
        campaign.setLUpdBy(userId);
        campaign.setCreatedDate(today);
        campaign.setLUpdDate(today);

        return campaignService.campaignRegistration(campaign);
    }

    @ApiOperation(value = "商家报名-获取活动banner", notes = "商家报名-获取活动banner")
    @SupplierLogin(validPermission = false)
    @GetMapping("/v1/campaign-get-banner/{number}")
    public Result<List<ScCampaignBannerListVO>> getCampaignBannerByNumber(@ApiIgnore UserVO userVo, @PathVariable("number") Integer number){
        return campaignService.getCampaignBannerByNumber(number);
    }

    private String getLanguage(@ApiIgnore HttpServletRequest request) {
        return LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.ZH_CN);
    }

    @SupplierLogin(validPermission = false)
    @ApiOperation(value = "商家活动报名提报产品-已添加商品列表", notes = "商家活动报名提报产品-已添加商品列表")
    @PostMapping("/v1/group-product-list")
    public Result<PageResult<CampaignSupplierProductLinkAggVO>> groupProductList(@ApiIgnore UserVO userVO, @RequestBody CampaignProductGroupLinkAggQueryDTO queryDTO) {
        Long supplierId = Optional.ofNullable(userVO).map(UserVO::getCurrentSupplier).map(BaseSupplierVO::getSupplierId).orElse(null);
        queryDTO.setOrgId(supplierId);
        queryDTO.setUserId(Optional.ofNullable(userVO).map(UserVO::getUserId).orElse(null));
        return campaignAggfeign.list(queryDTO);
    }

    @SupplierLogin(validPermission = false)
    @ApiOperation(value = "商家活动报名提报产品-AC设置的数据", notes = "商家活动报名提报产品-AC设置的数据")
    @GetMapping("/v1/campaign-setting-info")
    public Result<ScCampaignSubmissionSettingsBffDTO> getSettingInfo(@ApiParam("活动id") @RequestParam(name = "campaignId") Long campaignId) {
        return Result.success(campaignService.getSettingInfo(campaignId));
    }

    @AutoIdempotent
    @SupplierLogin(validPermission = false)
    @ApiOperation(value = "商家活动报名提报产品-批量添加商品到活动", notes = "商家活动报名提报产品-批量添加商品到活动")
    //@OperationLog(operation = OperationEnum.ADD, entityType = EntityType.CAMPAIGN, operationDesc = "Add product to supplier campaign", operationLogEntityId = "#addDTO.campaignId", operationLogOrgId = "#addDTO.orgId")
    @PostMapping("/v1/batch-add-product")
    public Result<Boolean> batchAddProduct(@ApiIgnore UserVO userVO, @Valid @RequestBody CampaignProductLinkBatchAddDTO addDTO) {
        addDTO.setUserId(userVO.getUserId());
        SuppUserUtil.checkCurrentOrgId(userVO, addDTO.getOrgId());
        return campaignAggfeign.batchAdd(addDTO);
    }
}
