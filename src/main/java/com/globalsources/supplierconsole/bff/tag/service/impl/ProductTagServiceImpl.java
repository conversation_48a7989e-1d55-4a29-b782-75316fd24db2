package com.globalsources.supplierconsole.bff.tag.service.impl;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.supplierconsole.agg.api.tag.feign.OrigProductTagFeign;
import com.globalsources.supplierconsole.agg.api.tag.model.dto.OrigProductTagAssignAggDTO;
import com.globalsources.supplierconsole.agg.api.tag.model.dto.OrigProductTagQueryAggDTO;
import com.globalsources.supplierconsole.agg.api.tag.model.dto.OrigProductTagUseQueryAggDTO;
import com.globalsources.supplierconsole.agg.api.tag.model.vo.OrigProductTagAggVO;
import com.globalsources.supplierconsole.agg.api.tag.model.vo.OrigProductTagUsageAggVO;
import com.globalsources.supplierconsole.bff.tag.dto.OrigProductTagAssign;
import com.globalsources.supplierconsole.bff.tag.dto.OrigProductTagQuery;
import com.globalsources.supplierconsole.bff.tag.dto.OrigProductTagUseQuery;
import com.globalsources.supplierconsole.bff.tag.service.ProductTagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
    * <p>
    *  服务实现类
    * </p>
    * <AUTHOR>
*/
@Slf4j
@Service
public class ProductTagServiceImpl implements ProductTagService {

    @Autowired
    private OrigProductTagFeign origProductTagFeign;

    @Override
    public Result<PageResult<OrigProductTagAggVO>> queryProductTagListPage(OrigProductTagQuery query, Long userId) {
        OrigProductTagQueryAggDTO origProductTagQueryAggDTO = OrikaMapperUtil.coverObject(query, OrigProductTagQueryAggDTO.class);
        return origProductTagFeign.queryProductTagList(origProductTagQueryAggDTO, userId);
    }

    @Override
    public Result<Boolean> saveProductTag(Long userId, Long orgId, String tagName) {
        if(Objects.isNull(userId) || Objects.isNull(orgId) || Objects.isNull(tagName)){
            log.warn("userId: {}, orgId: {}, tagName: {}", userId, orgId, tagName);
            return Result.failed(com.globalsources.framework.result.ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }
        return origProductTagFeign.saveProductTag(userId, orgId, tagName);
    }

    @Override
    public Result<Boolean> deleteProductTag(Long userId, Long orgId, Long tagId) {
        if(Objects.isNull(userId) || Objects.isNull(orgId) || Objects.isNull(tagId)){
            log.warn("userId: {}, orgId: {}, tagId: {}", userId, orgId, tagId);
            return Result.failed(com.globalsources.framework.result.ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }
        return origProductTagFeign.deleteProductTag(userId, orgId, tagId);
    }

    @Override
    public Result<Boolean> updateProductTag(Long userId, Long orgId, Long tagId, String tagName) {
        if(Objects.isNull(userId) || Objects.isNull(orgId) || Objects.isNull(tagName)){
            log.warn("userId: {}, orgId: {}, tagName: {}", userId, orgId, tagName);
            return Result.failed(com.globalsources.framework.result.ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }
        return origProductTagFeign.updateProductTag(userId, orgId, tagId, tagName);
    }

    @Override
    public Result<List<OrigProductTagUsageAggVO>> queryProductTagUsage(OrigProductTagUseQuery query, Long userId) {
        OrigProductTagUseQueryAggDTO origProductTagUseQueryAggDTO = OrikaMapperUtil.coverObject(query, OrigProductTagUseQueryAggDTO.class);
        return origProductTagFeign.queryProductTagUsage(origProductTagUseQueryAggDTO, userId);
    }

    @Override
    public Result<Boolean> assignProductTag(OrigProductTagAssign tagAssignDTO, Long userId) {
        OrigProductTagAssignAggDTO tagAssignAggDTO = OrikaMapperUtil.coverObject(tagAssignDTO, OrigProductTagAssignAggDTO.class);
        return origProductTagFeign.assignProductTag(tagAssignAggDTO,userId);
    }
}
