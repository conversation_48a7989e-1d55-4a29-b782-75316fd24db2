package com.globalsources.supplierconsole.bff.buyer.controller;

import com.globalsources.framework.annotation.SupplierLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.supplierconsole.bff.buyer.dto.TrafficDistQueryDTO;
import com.globalsources.supplierconsole.bff.buyer.dto.TrafficDistributionLinkDTO;
import com.globalsources.supplierconsole.bff.buyer.service.BuyerVisitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "我的访客")
@RequestMapping("/buyer/myvisitor")
@RestController
public class BuyerVisitorController {
    @Resource
    private BuyerVisitorService buyerVisitorService;

    @SupplierLogin(validPermission=false)
    @ApiOperation("获取访客列表")
    @PostMapping("/list")
    public Result<PageResult<TrafficDistributionLinkDTO>> getMyVisitorsList1(@Valid @RequestBody TrafficDistQueryDTO query){

        return buyerVisitorService.getMyVisitorList(query.getSupplierId(),query.getPageNum(),query.getPageSize(),query.getSortField(),query.getSortType());
    }

}
