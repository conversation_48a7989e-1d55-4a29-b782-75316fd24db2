package com.globalsources.rfx.service;

import com.globalsources.product.agg.api.dto.category.SuppProductCategoryLinkAggDTO;
import com.globalsources.product.agg.api.vo.ProductCategory;
import com.globalsources.product.agg.api.vo.ProductCategoryAttrVO;
import com.globalsources.product.agg.api.vo.ProductCategoryVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @since 2023/5/8
 */
public interface ICategoryService {

    /**
     * get category desc en by category id
     *
     * @param categoryId Long
     * @return String
     */
    String getCategoryDescEnById(Long categoryId);

    /**
     * get category by category id
     *
     * @param categoryId Long
     * @return ProductCategoryVO
     */
    ProductCategoryVO getCategoryById(Long categoryId);

    /**
     * 查询类别列表
     *
     * @param categoryIds
     * @return
     */
    List<ProductCategoryVO> getCategoryListByIds(List<Long> categoryIds);

    ProductCategory queryCategoryListById(Long categoryId, String language);

    List<ProductCategory> queryCategoryListByIds(List<Long> categoryIds, String language);

    /**
     * get product category attr by category l4 id
     *
     * @param l4CategoryId Long
     * @return List<ProductCategoryAttrVO>
     */
    List<ProductCategoryAttrVO> getProductCategoryAttr(Long l4CategoryId);

    /**
     * get product category attrs by l4 category ids
     *
     * @param l4CategoryIds List<Long>
     * @return List<ProductCategoryAttrVO>
     */
    List<ProductCategoryAttrVO> getProductCategoryAttrList(List<Long> l4CategoryIds);

    /**
     * 根据supplierId查询产品数量最多的类别
     *
     * @param supplierId
     * @param limit
     * @return
     */
    SuppProductCategoryLinkAggDTO getMostProdCategoryId(Long supplierId, int limit);

    /**
     * 根据supplierId列表查询产品数量最多的类别列表
     *
     * @param supplierIds
     * @param limit       default 5
     * @return
     */
    List<SuppProductCategoryLinkAggDTO> getMostProdCategoryIdList(List<Long> supplierIds, int limit);

    Map<Long, ProductCategory> getCategoryIdNameMapWithDelete(List<Long> categoryIdList);

    String getCategoryNameWithDelete(Long categoryId);
}
