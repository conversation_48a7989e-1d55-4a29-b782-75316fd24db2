package com.globalsources.rfx.util;

import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * <a>Title: ResultConverter </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: ResultConverter <a>
 *
 * <AUTHOR>
 * @date 2021/12/23 13:44
 */
@Slf4j
@UtilityClass
public class ResultConverter {

    /**
     * convert server transfer result
     * only success will get data, or else will
     * throw BusinessException
     * {@link BusinessException}
     *
     * @param result Result
     * @param <T>    T
     * @return T
     */
    public static <T> T convertData(Result<T> result) {
        Assert.notNull(result, "feign client transfer error, result is null");
        T data = null;
        if (ResultCode.CommonResultCode.SUCCESS.getCode().equals(result.getCode())) {
            data = result.getData();
        } else {
            log.error(result.getMsg(), new BusinessException(result.getCode(), result.getMsg()));
        }
        return data;
    }

    /**
     * convert Object to String
     *
     * @param obj Object
     * @return String
     */
    public static String convertString(Object obj) {
        return Objects.nonNull(obj) ? String.valueOf(obj) : null;
    }

    /**
     * convert Object to Integer
     *
     * @param obj Object
     * @return Integer
     */
    public static Integer convertInteger(Object obj) {
        return Objects.nonNull(obj) ? Integer.parseInt(String.valueOf(obj)) : null;
    }

    /**
     * convert Object to Long
     *
     * @param obj Object
     * @return Long
     */
    public static Long convertId(Object obj) {
        return Objects.nonNull(obj) ? Long.parseLong(String.valueOf(obj)) : null;
    }

}
