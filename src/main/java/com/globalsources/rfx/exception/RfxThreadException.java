package com.globalsources.rfx.exception;

import com.globalsources.framework.result.IResultCode;

/**
 * <a>Title: RfxThreadException </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfxThreadException <a>
 *
 * <AUTHOR>
 * @date 2021/11/19 9:02
 */
public class RfxThreadException extends RuntimeException {

    private static final long serialVersionUID = 2201800548165425255L;

    private String code;
    private String message;
    private Object data;

    public RfxThreadException(String code) {
        this.code = code;
    }

    public RfxThreadException(IResultCode resultCode) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
    }

    public RfxThreadException(String code, String message) {
        this.message = message;
        this.code = code;
    }

    public RfxThreadException(String code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    public Object getData() {
        return this.data;
    }
}
