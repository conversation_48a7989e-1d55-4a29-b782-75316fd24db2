package com.globalsources.rfx.bff.model.rfi.dto;

import com.globalsources.rfi.agg.request.AttachmentAggDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <a>Title: SupplierInquireFromDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/6-17:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="询盘提交对象", description="")
public class SupplierInquiryFromDTO implements Serializable {

    @ApiModelProperty(value = "供应商Id")
    @NotNull(message = "supplierId is not null")
    private Long supplierId;

    @NotNull(message = "message is not null")
    @Length(max = 5000)
    @ApiModelProperty(value = "message")
    private String message;

    @ApiModelProperty(value = "附件")
    private List<AttachmentAggDTO> attachmentList;

    @ApiModelProperty(value = "sessionId")
    @NotNull(message = "sessionId is not null")
    private String sessionId;

    @ApiModelProperty(value = "recommend matching suppliers and send this inquiry to them")
    private Boolean recommendFlag;

    @ApiModelProperty(value = "是否新注册用户")
    private Boolean regFlag;

    @ApiModelProperty(value = "inquiryPath")
    private String inquiryPath;
}
