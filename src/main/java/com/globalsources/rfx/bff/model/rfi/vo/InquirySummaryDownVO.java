/**
 * <a>Title: InquirySessionVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/29-8:38
 */
package com.globalsources.rfx.bff.model.rfi.vo;

import com.globalsources.database.api.model.vo.RequestUserSummaryVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquirySummaryDownVO implements Serializable {

    private List<RequestUserSummaryVO> all;

    private List<RequestUserSummaryVO> lastThirty;

    private List<RequestUserSummaryVO> lastSeven;
}
