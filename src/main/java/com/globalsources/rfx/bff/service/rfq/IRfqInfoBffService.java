package com.globalsources.rfx.bff.service.rfq;

import com.globalsources.database.api.enums.InquirySummaryPeriodTypeEnum;
import com.globalsources.database.api.model.vo.RFQSummaryVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.model.dto.req.*;
import com.globalsources.rfq.bff.api.model.vo.CategorySuggestVO;
import com.globalsources.rfq.bff.api.model.vo.ProductCategoryAttributeVO;
import com.globalsources.rfq.bff.api.model.vo.RfqInfoListVO;
import com.globalsources.rfq.bff.api.model.vo.RfqMobileInfoVO;
import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppInfoVO;
import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppNumsVO;
import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppQuotationDetailVO;
import com.globalsources.rfq.bff.api.model.vo.app.SupplierAppRfqDetailInfoVO;
import com.globalsources.rfq.bff.api.model.vo.app.SupplierAppRfqListVO;
import com.globalsources.rfq.bff.api.model.vo.app.SupplierAppRfqRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.seller.SellerRfqDataAnalysisVO;
import com.globalsources.rfq.bff.api.model.vo.seller.SellerRfqDetailVO;
import com.globalsources.rfq.core.api.model.dto.app.RfqAppInfoListRequestDTO;

import java.util.List;
import java.util.Map;

/**
 * <a>Title: IRfqInfoBffService </a>
 * <a>Author: Mike Chen <a>
 * <a>Description: IRfqInfoBffService <a>
 *
 * <AUTHOR> Chen
 * @date 2021/11/26 9:34
 */
public interface IRfqInfoBffService {

    PageResult<RfqInfoListVO> getRfqList(RfqInfoListRequestDTO requestDTO);

    Result createRfqInfo(RfqInfoCreateRequestDTO requestDTO);

    Result createAppRfqInfo(RfqAppCreateRequestDTO requestDTO);

    Result<PageResult<RfqBuyerAppInfoVO>> getRfqBuyerAppInfoList(RfqAppInfoListRequestDTO requestDTO);

    Result<RfqBuyerAppNumsVO> getRfqBuyerAppInfoListNums(Long userId);

    Result<Integer> rfqUnReadCount(Long userId);

    Result<Boolean> rfqReadStatus(RfqReadStatusRequestDTO requestDTO);

    Result<Boolean> rfqClose(RfqCloseRequestDTO requestDTO);

    Result<Boolean> star(RfqStarRequestDTO requestDTO);

    Result<Boolean> sellerRfqReadStatus(RfqSupplierReadStatusRequestDTO requestDTO);

    Result<SellerRfqDetailVO> sellerRfqDetail(RfqSupplierDetailRequestDTO requestDTO);

    PageResult<RfqInfoListVO> rfqStarList(RfqStarListRequestDTO requestDTO);

    Result<List<ProductCategoryAttributeVO>> getCategoryAttribute(Long l4CategoryId);

    Result inSiteNotification(RfqInSiteNotificationRequestDTO build);

    Result<SellerRfqDataAnalysisVO> sellerDataAnalysis(RfqSellerDataAnalysisRequestDTO requestDTO);

    Result<Integer> rfqStarUnReadCount(Long userId);

    Result<List<CategorySuggestVO>> categorySuggest(RfqKeywordDTO dto);

    Result<Boolean> delete(RfqSellerDeleteRequestDTO requestDTO, Boolean superAdmAccount);

    Result<Boolean> deleteRecover(Long userId, Long supplierId, List<String> rfqIds);

    Result<RfqBuyerAppQuotationDetailVO> getRfqBuyerAppDetail(Long userId, String rfqId);

    Result<SupplierAppRfqListVO> getSupplierAppRfqInfoList(SupplierAppRfqRequestDTO requestDTO);

    Result<SupplierAppRfqDetailInfoVO> supplierRfqDetail(Long supplierId, Long userId, String rfqId);

    PageResult<RfqMobileInfoVO> getRfqMobileInfoList(RfqMobileInfoRequestDTO toRfqMobileInfoRequest);

    Result createMobileRfqInfo(RfqMobileCreateRequestDTO toRfqMobileCreateRequest);

    RFQSummaryVO rfqSummary(Long supplierId, InquirySummaryPeriodTypeEnum dateType,Long supplierUserId);

    Map<String, Object> getRFQSummaryData(Long supplierId, String supplierName,String lang);
}
