package com.globalsources.rfx.bff.service.common.impl;

import com.globalsources.agg.supplier.api.feign.SupplierRoleAggFeign;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfx.bff.model.common.SupplierUserInfoVO;
import com.globalsources.rfx.bff.service.common.SupplierRoleService;
import com.globalsources.rfx.bff.util.SupplierUserInfoUtil;
import com.globalsources.user.api.feign.UserQueryFeign;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <a>Title: SupplierRoleServiceImpl </a>
 * <a>Author: Mike Chen <a>
 * <a>Description: SupplierRoleServiceImpl <a>
 *
 * <AUTHOR> Chen
 * @date 2021/11/30 9:35
 */
@Slf4j
@Component
@AllArgsConstructor
public class SupplierRoleServiceImpl implements SupplierRoleService {

    private final UserQueryFeign userQueryFeign;
    private final SupplierRoleAggFeign supplierRoleAggFeign;

    @Override
    public List<SupplierUserInfoVO> getSupplierSubUser(Long supplierId) {
        Assert.notNull(supplierId, "supplier id can not be null");
        List<SupplierUserInfoVO> userList = Lists.newArrayList();
        Result<List<Long>> userIdsByOrgId = supplierRoleAggFeign.getUserIdsBySupplierId(supplierId, false);
        List<Long> userIds = ResultUtil.getData(userIdsByOrgId, "failed to get userIds by supplierId : " + supplierId);
        if (CollectionUtils.isNotEmpty(userIds)) {
            Result<List<UserVO>> users = userQueryFeign.findUsersByUserId(userIds);
            List<UserVO> supplierUserList = ResultUtil.getData(users, "get User Ids By Supplier Id failed, userIds:" + userIds);
            if (CollectionUtils.isNotEmpty(supplierUserList)) {
                userList = supplierUserList.parallelStream().filter(Objects::nonNull).map(user -> SupplierUserInfoVO.builder()
                        .userId(user.getUserId())
                        .username(SupplierUserInfoUtil.convertUsername(user.getFirstName(), user.getLastName()))
                        .email(user.getEmail()).build()).distinct().sorted(Comparator.comparing(SupplierUserInfoVO::getUsername, String.CASE_INSENSITIVE_ORDER)).collect(Collectors.toList());
            }
        }
        return userList;
    }

}
