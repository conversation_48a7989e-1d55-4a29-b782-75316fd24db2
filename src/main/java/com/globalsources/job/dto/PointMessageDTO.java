package com.globalsources.job.dto;

import com.globalsources.user.api.enums.TransCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class PointMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "业务类型")
    private TransCodeEnum action;

    @ApiModelProperty(value = "业务id(rfiId, rfqId, orderId)")
    private String objectId;
}