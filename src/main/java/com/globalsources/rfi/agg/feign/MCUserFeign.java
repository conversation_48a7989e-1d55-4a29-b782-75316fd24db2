/**
 * <a>Title: <PERSON><PERSON><PERSON><PERSON>eign </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/26-21:24
 */
package com.globalsources.rfi.agg.feign;

import com.globalsources.framework.result.Result;
import com.globalsources.rfi.agg.core.dto.McUserDTO;
import com.globalsources.rfi.agg.core.dto.mc.SimpleMcUserCoreDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "gsol-rfi-agg", path = "/gsol-rfi-agg/message-user")
public interface MCUserFeign {

    @ApiOperation(value = "获取message邮箱地址")
    @GetMapping(value = "/query-mc-user/{supplierId}/{userId}")
    Result<String> queryMCUser(@PathVariable Long supplierId, @PathVariable Long userId);

    @ApiOperation(value = "创建邮箱地址")
    @PostMapping(value = "/add-mc-user")
    Result<Integer> addMCUser(@RequestBody McUserDTO mcUserDTO);

    @ApiOperation(value = "get simple mc user infos", notes = "需要mc的userId")
    @GetMapping(value = "v1/simple-users")
    Result<List<SimpleMcUserCoreDTO>> getSimpleMcUsersByUserId(@RequestParam("userId") Long userId);

    @ApiOperation(value = "get mc request thread threadId", notes = "mc的离线回复emailId")
    @GetMapping(value = "v1/request-thread-id")
    Result<String> getRequestThreadId(@RequestParam("emailId") Long emailId);
}
