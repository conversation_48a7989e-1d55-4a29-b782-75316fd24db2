package com.globalsources.rfi.agg.core.dto.rfi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryBuyerStatusCoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 询盘编号
     */
    private String threadId;

    /**
     * 供应商编号
     */
    private Long buyerId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 最后修改时间
     */
    private Date lUpdDate;

    /**
     * 是否加星
     */
    private Boolean starredFlag;

    /**
     * 是否已回复
     */
    private Boolean replyFlag;

    /**
     * 是否已读
     */
    private Boolean readFlag;

    /**
     * 是否已删除
     */
    private Boolean deleteFlag;

    /**
     * 是否是垃圾询盘
     */
    private Boolean rubbishFlag;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 买家电子邮箱
     */
    private String emailAddr;

    private String firstName;

    private String lastName;

}
