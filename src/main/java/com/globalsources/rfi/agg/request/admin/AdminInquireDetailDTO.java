package com.globalsources.rfi.agg.request.admin;

import com.globalsources.rfi.agg.dto.user.BuyerUserInfoDTO;
import com.globalsources.rfi.agg.dto.user.SellerUserInfoDTO;
import com.globalsources.rfi.agg.response.ResponseAttachmentVO;
import com.globalsources.rfi.agg.response.InquiryProdVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/3 10:15
 */
@Data
@ApiModel(description = "rfi详情(admin console)")
public class AdminInquireDetailDTO implements Serializable {

    private static final long serialVersionUID = -4138913670027590203L;

    @ApiModelProperty(value = "买家用户信息")
    private BuyerUserInfoDTO buyerUserInfo;

    @ApiModelProperty(value = "卖家用户信息")
    private SellerUserInfoDTO sellerUserInfo;

    @ApiModelProperty(value = "产品信息")
    private InquiryProdVO inquiryProd;

    @ApiModelProperty(value = "附件list")
    private List<ResponseAttachmentVO> attachments;

    @ApiModelProperty(value = "消息")
    private String message;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "询盘id")
    private String inquiryId;

    @ApiModelProperty(value = "submit date")
    private Date createDate;

    @ApiModelProperty(value = "询盘类型 SUPPLIER, SUPPLIER, PRODUCT_UPSELL")
    private String inquiryType;

    @ApiModelProperty(value = "最新状态 枚举：1：Send，2：Under Review，3：Release，4：Reply，5：Reassign，6：Pass，7：Block，8：Reject")
    private Integer threadStatus;

    @ApiModelProperty(value = "最新状态：Send，Under Review，Release，Reply，Reassign，Pass，Block, Reject")
    private String status;

    @ApiModelProperty(value = "Upsell from")
    private String parInquiryId;

    @ApiModelProperty(value = "rif来源(平台)")
    private String rfiSources;


    @ApiModelProperty(value = "状态记录")
    private List<AdminRfiThreadStatusDTO> statusRecords;
}
