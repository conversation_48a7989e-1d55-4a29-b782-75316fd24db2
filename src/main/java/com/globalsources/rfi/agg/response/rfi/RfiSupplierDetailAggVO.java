package com.globalsources.rfi.agg.response.rfi;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <a>Title: RfiSupplierDetailVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/26-14:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RfiSupplierDetailAggVO {

    @ApiModelProperty(value = "threadId")
    private String inquiryId;

    @ApiModelProperty(value = "供应商Id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商公司")
    private String supplierName;

    @ApiModelProperty(value = "公司头像")
    private String photo;

    @ApiModelProperty(value = "产品Id")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品图片链接")
    private String productImageUrl;

    @ApiModelProperty(value = "最小订单量")
    private Integer minOrderNumber;

    @ApiModelProperty(value = "最小订单单位")
    private String minOrderUnit;

    @ApiModelProperty(value = "categoryId1")
    private Long categoryL1;

    @ApiModelProperty(value = "categoryId2")
    private Long categoryL2;

    @ApiModelProperty(value = "categoryId3")
    private Long categoryL3;

    @ApiModelProperty(value = "categoryId4")
    private Long categoryL4;

    @ApiModelProperty(value = "P0 to P6 (供应商的星级，showroom type)")
    private Long maxStartLevel;

    @ApiModelProperty(value = "供应商的性质, ADV, AGG or FL （三种不同的供应商类型）")
    private String supplierType;

    @ApiModelProperty(value = "供应商负责人Id")
    private Long supplierUserId;

}
