/**
 * <a>Title: ISupplierInquiryServiceImpl </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/13-21:33
 */
package com.globalsources.rfi.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.agg.admin.api.feign.RfqBlackListFeign;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.agg.supplier.api.model.dto.organization.OrganizationDTO;
import com.globalsources.common.api.vo.DictionaryItemVO;
import com.globalsources.email.enums.RfxEmailTemplateEnum;
import com.globalsources.email.po.EmailModelPO;
import com.globalsources.email.vo.InquiryProductVO;
import com.globalsources.email.vo.InquiryReplyDetailVO;
import com.globalsources.email.vo.InquiryReplyVO;
import com.globalsources.file.api.dto.FileReqDTO;
import com.globalsources.file.api.feign.FileFeign;
import com.globalsources.file.api.vo.UploadVO;
import com.globalsources.framework.enums.DicGroupNameEnum;
import com.globalsources.framework.enums.SellerRoleEnum;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.IResultCode;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.BeanUtil;
import com.globalsources.framework.utils.CipherUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.BaseSupplierVO;
import com.globalsources.framework.vo.UserBaseProfileVO;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.message.dto.MessageNoticeEntity;
import com.globalsources.message.dto.NoticeMsgDataDTO;
import com.globalsources.message.dto.NoticePushDTO;
import com.globalsources.message.enums.MessageTypeEnum;
import com.globalsources.message.enums.NoticeBizTypeEnum;
import com.globalsources.message.enums.NoticeCopyTypeEnum;
import com.globalsources.message.feign.MessageAggFeign;
import com.globalsources.product.agg.api.dto.product.ProductDetailVo;
import com.globalsources.product.agg.api.vo.ProductCategoryVO;
import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.product.agg.api.vo.ProductTrackingVO;
import com.globalsources.rfi.agg.constants.RedisCachePrefix;
import com.globalsources.rfi.agg.constants.RfiCommonConstants;
import com.globalsources.rfi.agg.constants.RfiSourceEnum;
import com.globalsources.rfi.agg.core.dto.InquireAllEmailChatDTO;
import com.globalsources.rfi.agg.core.dto.InquiryBuyerStatusDTO;
import com.globalsources.rfi.agg.core.dto.InquirySupplierStatusDTO;
import com.globalsources.rfi.agg.core.dto.attachment.AttachmentCoreDTO;
import com.globalsources.rfi.agg.core.dto.mc.RequestRfiReadDTO;
import com.globalsources.rfi.agg.core.vo.*;
import com.globalsources.rfi.agg.core.vo.rfi.RfiInquiryEmailAddressCoreVO;
import com.globalsources.rfi.agg.dto.chat.InquiryChatMessageAggDTO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryDeleteDTO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryStatusDTO;
import com.globalsources.rfi.agg.dto.inquiry.SupplierInquiryListExportDTO;
import com.globalsources.rfi.agg.email.RfiMsgVO;
import com.globalsources.rfi.agg.enums.*;
import com.globalsources.rfi.agg.request.*;
import com.globalsources.rfi.agg.request.InquiryChatAGGDTO;
import com.globalsources.rfi.agg.request.InquiryChatDTO;
import com.globalsources.rfi.agg.request.SupplierInquireDTO;
import com.globalsources.rfi.agg.request.admin.InquiryLogSaveDTO;
import com.globalsources.rfi.agg.request.message.InquiryMessageListDTO;
import com.globalsources.rfi.agg.request.product.InquiryProductCategoryAttrCoreDTO;
import com.globalsources.rfi.agg.request.supplier.InquiryTypeListDTO;
import com.globalsources.rfi.agg.request.supplier.SupplierReassignDTO;
import com.globalsources.rfi.agg.request.supplier.SupplierStatusCountDTO;
import com.globalsources.rfi.agg.response.*;
import com.globalsources.rfi.agg.response.APPInquiryDetailVO;
import com.globalsources.rfi.agg.response.APPInquiryReplyChatVO;
import com.globalsources.rfi.agg.response.IdsVO;
import com.globalsources.rfi.agg.response.InquiryDetailVO;
import com.globalsources.rfi.agg.response.InquiryProdVO;
import com.globalsources.rfi.agg.response.SupplierInquiryListTotalVO;
import com.globalsources.rfi.agg.response.SupplierInquiryTotalVO;
import com.globalsources.rfi.agg.response.SupplierInquiryVO;
import com.globalsources.rfi.agg.response.detail.InquiryBuyerDetailVO;
import com.globalsources.rfi.agg.response.detail.InquiryBuyerInfo;
import com.globalsources.rfi.agg.response.detail.InquiryCompanyVO;
import com.globalsources.rfi.agg.response.detail.InquiryMessageVO;
import com.globalsources.rfi.agg.response.detail.InquiryProductInfo;
import com.globalsources.rfi.agg.response.detail.InquiryReplyDetailChatVO;
import com.globalsources.rfi.agg.response.detail.SupplierInquiryDetailVO;
import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeVO;
import com.globalsources.rfi.agg.response.rfi.RfiInquiryEmailAddressAggVO;
import com.globalsources.rfi.agg.response.rfi.RfiSupplierDetailAggVO;
import com.globalsources.rfi.agg.response.rfi.SupplierInquiryListDownloadVO;
import com.globalsources.rfi.constants.InquiryCoreConstants;
import com.globalsources.rfi.data.dao.*;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.data.entity.InquiryLogEntity;
import com.globalsources.rfi.data.entity.InquirySupplierStatusEntity;
import com.globalsources.rfi.service.InquiryAllItemService;
import com.globalsources.rfi.service.InquiryProductCategoryAttrService;
import com.globalsources.rfi.service.*;
import com.globalsources.rfi.utils.*;
import com.globalsources.rfx.service.*;
import com.globalsources.rfi.service.InquiryAllFileAttachmentService;
import com.globalsources.rfi.service.InquiryStatusService;
import com.globalsources.rfi.service.SupplierInquiryService;
import com.globalsources.rfi.service.RabbitService;
import com.globalsources.rfi.utils.DictCountryUtils;
import com.globalsources.rfi.utils.FileUtil;
import com.globalsources.rfi.utils.InquiryEmailAddressUtil;
import com.globalsources.rfi.utils.InquiryReplyUtil;
import com.globalsources.rfi.utils.LocalDateUtils;
import com.globalsources.rfi.utils.UserInfoUtil;
import com.globalsources.user.api.vo.BaseUserInfoVO;
import com.globalsources.user.api.vo.UserFavoriteStatusVO;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.ognl.OgnlException;
import org.apache.logging.log4j.util.Strings;
import org.apache.shardingsphere.api.hint.HintManager;
import org.joda.time.DateTime;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.beans.IntrospectionException;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class SupplierInquiryServiceImpl implements SupplierInquiryService {

    @Value("${third.sso.dataKey}")
    private String secretKey;

    private static final String HOST_NAME = "www.globalsources.com";

    ExecutorService executorService = Executors.newFixedThreadPool(8);

    @Autowired
    private IChatService chatService;

    @Autowired
    private InquiryAllService inquireAllService;

    @Autowired
    private DictCountryUtils dictCountryUtils;

    @Autowired
    private RabbitService rabbitService;

    @Autowired
    private SupplierInquiryService supplierInquiryService;

    @Autowired
    private InquiryEmailAddressUtil inquiryEmailAddressUtil;

    @Autowired
    private InquiryAllFileAttachmentService inquireAllFileAttachmentService;

    @Autowired
    private RfqBlackListFeign blackListFeign;

    @Autowired
    private RabbitService rfiMsgService;

    @Autowired
    private InquiryAllItemDao inquiryAllItemDao;

    @Autowired
    private InquirySupplierStatusDao inquirySupplierStatusDao;

    @Autowired
    private InquiryAllEmailChatDao inquiryAllEmailChatDao;

    @Autowired
    private InquiryAllDao inquiryAllDao;

    @Autowired
    private InquiryStatusService inquiryStatusService;

    @Autowired
    private InquiryAllEmailChatService inquireAllEmailChatService;

    @Autowired
    private BuyerInquiryService buyerInquiryService;

    @Autowired
    private InquiryProductCategoryAttrService inquiryProductCategoryAttrService;

    @Autowired
    private InquiryAllEmailAddressService inquiryAllEmailAddressService;

    @Autowired
    private InquiryAllEmailService inquiryAllEmailService;

    @Autowired
    private InquiryAllItemService inquiryAllItemService;

    @Autowired
    private IProductService productService;

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ISupplierService supplierService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private InquiryAllEmailDao inquiryAllEmailDao;

    private static final String AMAZON_ATTACHMENT_DOMAIN="https://s3.ap-east-1.amazonaws.com/gsol.rfi/";

    private static final String AMAZON_ATTACHMENT_FASTER_DOMAIN="https://rfiresource.globalsources.com/gsol.rfi/";

    private static final String NA = "N/A";

    private static final String COUNTRY = "Country";

    private static final String EXPORT_FILE_NAME = "/%s-%s-%s RFI List-%s(%s).xlsx";

    private static final String EXPORT_FILE_TEMPLATE_PATH_INQUIRY_LIST = "template/inquiryList.xlsx";

    private static final String DEFAULT_CSO_EMAIL = "<EMAIL>";

    private static final String DEFAULT_CSO_USER_FIRST_NAME = "GS";
    private static final String DEFAULT_CSO_USER_LAST_NAME = "hotline";

    @Value(value = "${rfi.delete.potential.opportunity.dyas:60}")
    private int deletePotentialOpportunityDays;

    @Autowired
    private AmazonUtil amazonUtil;

    @Autowired
    private FileFeign fileFeign;

    @Autowired
    private MessageAggFeign messageAggFeign;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private InquiryLogDao inquiryLogDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RfqBlackListFeign rfqBlackListFeign;

    @Override
    public PageResult<SupplierInquiryVO> inquiryList(SupplierInquireDTO supplierInquireDTO, Long supplierId, Long supplierUserId, Long roleType) {
        Boolean suppAdminFlag = SellerRoleEnum.SUPER_ADMIN.roleId().equals(roleType);
        if (CollectionUtils.isNotEmpty(supplierInquireDTO.getBuyerLocationList())) {
            setCountryCodeList(supplierInquireDTO);
        }
        if(CollectionUtils.isNotEmpty(supplierInquireDTO.getRfiSourceList())){
            setRfiSourceList(supplierInquireDTO);
        }
        supplierInquireDTO.setSortField(Optional.ofNullable(InquiryCoreConstants.SortField.match(supplierInquireDTO.getSortFieldKey())).orElse(InquiryCoreConstants.SortField.L_UPD_DATE.getValue()));
        supplierInquireDTO.setOrderType(Optional.ofNullable(InquiryCoreConstants.OrderType.match(supplierInquireDTO.getOrderTypeKey())).orElse(InquiryCoreConstants.OrderType.DESC.getValue()));

        Page<SupplierInquiryVO> page = new Page<>(supplierInquireDTO.getPageNum(), supplierInquireDTO.getPageSize());
        IPage<SupplierInquiryVO> pageResult = inquiryAllDao.getSupplerTablePage(page, supplierInquireDTO, supplierId, supplierUserId, suppAdminFlag);
        // 传入inquireId   则判断是否存在列表  不存在多查询一次
        if (StringUtils.isNotBlank(supplierInquireDTO.getInquiryId())){
            boolean isContain = pageResult.getRecords().stream().anyMatch(m->m.getInquiryId().equals(supplierInquireDTO.getInquiryId()));
            if (Boolean.FALSE.equals(isContain)){
                // 不包含inquiry 查询一次
                supplierInquireDTO.setInquiryFlag("true");
                SupplierInquiryVO supplierInquiryVO = inquiryAllDao.getSupplierTableByThreadId(supplierInquireDTO, supplierId, supplierUserId, suppAdminFlag);
                if (null != supplierInquiryVO){
                    if(CollectionUtils.isEmpty(pageResult.getRecords())){
                        List<SupplierInquiryVO> recordList = new ArrayList<>();
                        pageResult.setRecords(recordList);
                    }
                    pageResult.getRecords().add(0,supplierInquiryVO);
                }
            }
        }
        PageResult<SupplierInquiryVO> data = new PageResult<>();
        data.setList(pageResult.getRecords());
        data.setTotal(pageResult.getTotal());
        data.setPageNum(pageResult.getCurrent());
        data.setPageSize(pageResult.getSize());
        data.setTotalPage(pageResult.getPages());

        data.getList().forEach(item -> {
            item.setCountry(StringUtils.isNotEmpty(item.getCountry()) ? dictCountryUtils.convertMapForOrderStatus(item.getCountry()) : "");
            item.setFirstInquiryFlag(!Objects.equals("Y", item.getSupplierType()));

            item.setAdminTradeshowFlag(RfiSourceEnum.isAdminTradeshowFlag(item.getRfiSource()));
        });

        return data;
    }

    private void setRfiSourceList(SupplierInquireDTO supplierInquireDTO) {
        if (supplierInquireDTO.getRfiSourceList().contains(RfiSourceEnum.APP.getValue())) {
            supplierInquireDTO.getRfiSourceList().add(RfiSourceEnum.IOS.getValue());
            supplierInquireDTO.getRfiSourceList().add(RfiSourceEnum.ANDROID.getValue());
        }
        if (supplierInquireDTO.getRfiSourceList().contains(RfiSourceEnum.ADMIN_TRADE_SHOW_SOURCE.getValue())) {
            supplierInquireDTO.getRfiSourceList().addAll(RfiSourceEnum.getAdminTradeshowSource());
        }
        if (supplierInquireDTO.getRfiSourceList().contains(RfiSourceEnum.ADMIN_OTHER_SOURCE.getValue())) {
            supplierInquireDTO.getRfiSourceList().addAll(RfiSourceEnum.getAdminOtherSource());
        }
    }
    private void setRfiSourceList(SupplierStatusCountDTO supplierInquireDTO) {
        if (supplierInquireDTO.getRfiSourceList().contains(RfiSourceEnum.APP.getValue())) {
            supplierInquireDTO.getRfiSourceList().add(RfiSourceEnum.IOS.getValue());
            supplierInquireDTO.getRfiSourceList().add(RfiSourceEnum.ANDROID.getValue());
        }
        if (supplierInquireDTO.getRfiSourceList().contains(RfiSourceEnum.ADMIN_TRADE_SHOW_SOURCE.getValue())) {
            supplierInquireDTO.getRfiSourceList().addAll(RfiSourceEnum.getAdminTradeshowSource());
        }
        if (supplierInquireDTO.getRfiSourceList().contains(RfiSourceEnum.ADMIN_OTHER_SOURCE.getValue())) {
            supplierInquireDTO.getRfiSourceList().addAll(RfiSourceEnum.getAdminOtherSource());
        }
    }

    private void buildRfiSourceList(List<Integer> rfiSourceList) {
        if (rfiSourceList.contains(RfiSourceEnum.APP.getValue())) {
            rfiSourceList.add(RfiSourceEnum.IOS.getValue());
            rfiSourceList.add(RfiSourceEnum.ANDROID.getValue());
        }
        if (rfiSourceList.contains(RfiSourceEnum.ADMIN_TRADE_SHOW_SOURCE.getValue())) {
            rfiSourceList.addAll(RfiSourceEnum.getAdminTradeshowSource());
        }
        if (rfiSourceList.contains(RfiSourceEnum.ADMIN_OTHER_SOURCE.getValue())) {
            rfiSourceList.addAll(RfiSourceEnum.getAdminOtherSource());
        }
    }

    @Override
    public void inquiryDownloadListV2(SupplierInquiryListExportDTO dto) {
        log.info("inquiryDownloadListV2 start");

        //先立个flag，防止重复导出
        redisTemplate.opsForValue().set(RedisCachePrefix.INQUIRY_SUPPLIER_LIST_EXPORT_FLAG + dto.getUserId() + dto.getSupplierId() + dto.getTabType(),JSON.toJSONString(dto),10, TimeUnit.MINUTES);
        log.info("inquiryDownloadListV2 set flag succ");

        String exportFileName = String.format(EXPORT_FILE_NAME, new DateTime(dto.getStartDateTime()).toString("yyyy-MM-dd"), new DateTime(dto.getEndDateTime()).toString("yyyy-MM-dd"),Objects.equals(1,dto.getTabType())?"1-1":"1-Many", dto.getUserName(), dto.getSupplierId()).replace("//+", "%20");

        File file = new File(exportFileName);
        log.info("inquiryDownloadListV2 file name:{}",file.getName());

        // 分批获取数据
        List<SupplierInquiryListDownloadVO> dataList = inquiryAllDao.getSupplerInquiryDownloadListV2(dto);
        List<SupplierInquiryListDownloadVO> batchList;
        Map<String, String> countryMap = dictService.getDicKeyValueMap(COUNTRY, "enus");

        // 分批处理
        int batchSize = 1000;
        int totalSize = dataList.size();
        int batchCount = (totalSize + batchSize - 1) / batchSize; // 计算总批次数
        for (int i = 0; i < totalSize; i += batchSize) {
            int batchNum = i / batchSize + 1;
            int endIndex = Math.min(i + batchSize, totalSize);
            batchList = dataList.subList(i, endIndex);
            log.info("inquiryDownloadListV2 addInfo - Processing batch {}/{}, size: {}", batchNum, batchCount, batchList.size());
            // 处理当前批次的数据
            addInfo(batchList,countryMap);
            log.info("inquiryDownloadListV2 addInfo - Completed processing batch {}/{}", batchNum, batchCount);
        }

        log.info("inquiryDownloadListV2 completed data retrieval - Total records: {}", dataList.size());

        try (InputStream is = getClass().getClassLoader().getResourceAsStream(EXPORT_FILE_TEMPLATE_PATH_INQUIRY_LIST)) {
            Map<String, Object> model = new HashMap<>();
            model.put("dataList",dataList);
            model.put("startDate", dto.getStartDateTime());
            model.put("endDate", dto.getEndDateTime());
            Context context = new Context(model);
            JxlsHelper.getInstance().processTemplate(is, Files.newOutputStream(file.toPath()), context);
            log.info("inquiryDownloadListV2 file create succ");
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(), "multipart/form-data", new FileInputStream(file));
            UploadVO uploadResult = ResultUtil.getDataOrElseThrow(fileFeign.fileUpload(1,multipartFile));
            log.info("inquiryDownloadListV2 file upload succ,result:{}",uploadResult);
            String fasterUrl = getFasterUrl(uploadResult.getUrl());
            log.info("inquiryDownloadListV2 fasterUrl:{}",fasterUrl);

            /**
             * userId+supplierId+tabType
             * 因为message服务里的request_id字段长度限制，所以这里是相加，不是拼接
             */
            String requestId = String.valueOf(dto.getUserId() + dto.getSupplierId()+dto.getTabType()+dto.getStartDateTime().getTime()+dto.getEndDateTime().getTime());
            log.info("inquiryDownloadListV2 file, userId:{},supplierId:{},tabType:{}, requestId:{}",dto.getUserId(),dto.getSupplierId(),dto.getTabType(),requestId);

            redisTemplate.opsForValue().set(RedisCachePrefix.INQUIRY_SUPPLIER_LIST_EXPORT_URL+requestId,fasterUrl);
            log.info("inquiryDownloadListV2 set cache succ , requestId:{}",requestId);

            MessageNoticeEntity messageNoticeEntity = MessageNoticeEntity.builder()
                    .businessId(requestId)
                    .messageType(MessageTypeEnum.RFI_DOWN.getValue())
                    .userId(dto.getUserId())
                    .roleType(2)
                    .supplierId(dto.getSupplierId())
                    .build();

            NoticePushDTO noticePushDTO = new NoticePushDTO();
            noticePushDTO.setBizType(NoticeBizTypeEnum.RFI_EXPORT_SUCC_NOTICE.name());
            noticePushDTO.setMessageNoticeEntityList(Collections.singletonList(messageNoticeEntity));
            noticePushDTO.setNoticeCopyType(NoticeCopyTypeEnum.RFI_DOWNLOAD_SUCC.name());
            noticePushDTO.setNoticeMsgDataDTOList(Collections.singletonList(new NoticeMsgDataDTO()));
            log.info("inquiryDownloadListV2 noticePushDTO:{}",noticePushDTO);

            Result<Boolean> result = messageAggFeign.insertMessage(noticePushDTO);
            log.info("inquiryDownloadListV2 notice push result:{}",result);

            if (Boolean.FALSE.equals(result.getData())) {
                log.error("inquiryDownloadListV2 notice push fail,dto:{} result{}",dto,result);
                log.warn("inquiryDownloadListV2 notice push fail, remove key, result:{}",redisTemplate.delete(RedisCachePrefix.INQUIRY_SUPPLIER_LIST_EXPORT_URL+requestId));
            }
        } catch (Exception e) {
            log.error("inquiryDownloadListV2 error dto:{}, error:{}", dto,JSON.toJSONString(e));
        }finally {
            if(file.delete()){
                log.info("inquiryDownloadListV2 file delete succ");
            }else{
                log.warn("inquiryDownloadListV2 file delete fail");
            }
            redisTemplate.delete(RedisCachePrefix.INQUIRY_SUPPLIER_LIST_EXPORT_FLAG + dto.getUserId() + dto.getSupplierId() + dto.getTabType());

        }
    }

    private static String getFasterUrl(String url) {
        try {
            if(StringUtils.isNotBlank(url) && url.contains(AMAZON_ATTACHMENT_DOMAIN)){
                return url.replace(AMAZON_ATTACHMENT_DOMAIN,AMAZON_ATTACHMENT_FASTER_DOMAIN);
            }
        }catch (Exception e){
            log.error("getFasterUrl error url:{},error:{}",url,JSON.toJSONString(e));
        }
        return url;
    }

    private void addInfo(List<SupplierInquiryListDownloadVO> inquiryList,Map<String, String> countryMap) {
        Map<Long, BaseUserInfoVO> buyerInfoMap = getBuyerInfoMap(inquiryList);

        BaseUserInfoVO user = null;
        for (SupplierInquiryListDownloadVO inquiry : inquiryList) {
            if (StringUtils.isEmpty(inquiry.getProductName())) {
                inquiry.setProductName(NA);
            }
            if (StringUtils.isEmpty(inquiry.getModelNumber())) {
                inquiry.setModelNumber(NA);
            }
            user = buyerInfoMap.get(inquiry.getBuyerId());
            if (Objects.isNull(user)) {
                continue;
            }
            inquiry.setBuyerRegisterDate(user.getCreateDate());
            inquiry.setBuyerVerified(user.isVerifiedFlag() ? "Y" : "N");
            inquiry.setBuyerEmailAddress(user.getEmail());
            inquiry.setCountry(countryMap.get(inquiry.getCountry()));
            inquiry.setCompanyName(user.getCompanyName());
            inquiry.setWebsiteUrl(StringUtils.isBlank(user.getWebsiteUrl()) ? Strings.EMPTY :user.getWebsiteUrl());
            inquiry.setBuyerPhoneNumber(UserInfoUtil.getPhoneNumber(user.getTelCountryCode(), user.getTelAreaCode(), user.getPhoneNumber()));
        }
    }

    private Map<Long, BaseUserInfoVO> getBuyerInfoMap(List<SupplierInquiryListDownloadVO> inquiryList) {
        Map<Long, BaseUserInfoVO> result = new HashMap<>(inquiryList.size());
        List<Long> buyerIds = inquiryList.stream().map(SupplierInquiryListDownloadVO::getBuyerId).distinct().collect(Collectors.toList());

        // 分批处理，每批100个
        int batchSize = 100;
        int totalSize = buyerIds.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<Long> batchBuyerIds = buyerIds.subList(i, endIndex);

            List<BaseUserInfoVO> batchBuyerList = userService.getBaseUserInfoList(batchBuyerIds);
            if (CollectionUtils.isNotEmpty(batchBuyerList)) {
                for (BaseUserInfoVO user : batchBuyerList) {
                    if (Objects.nonNull(user)) {
                        result.put(user.getUserId(), user);
                    }
                }
            }

            log.info("getBuyerInfoMap batch: {}/{}, 当前批次大小: {}", endIndex, totalSize, batchBuyerIds.size());
        }

        return result;
    }
    @Override
    public Result<SupplierInquiryListTotalVO> supplierStatusCount(SupplierStatusCountDTO supplierStatusCountDTO) throws ExecutionException, InterruptedException {
        if (CollectionUtils.isNotEmpty(supplierStatusCountDTO.getBuyerLocationList())) {
            supplierStatusCountDTO.setCountryCodeListFilter(countryCodeDic(supplierStatusCountDTO.getCountryCodeListFilter(),supplierStatusCountDTO.getBuyerLocationList()));
        }
        if (CollectionUtils.isNotEmpty(supplierStatusCountDTO.getRfiSourceList())) {
            setRfiSourceList(supplierStatusCountDTO);
        }
        CompletableFuture<Integer> all = CompletableFuture.supplyAsync(() -> inquirySupplierStatusDao.supplierStatusAllCount(supplierStatusCountDTO),executorService);
        CompletableFuture<Integer> noRead = CompletableFuture.supplyAsync(() -> inquirySupplierStatusDao.supplierStatusUnReadCount(supplierStatusCountDTO),executorService);
        CompletableFuture<Integer> noReply = CompletableFuture.supplyAsync(() -> inquirySupplierStatusDao.supplierStatusUnReplyCount(supplierStatusCountDTO),executorService);
        CompletableFuture<Integer> replied = CompletableFuture.supplyAsync(() -> inquirySupplierStatusDao.supplierStatusRepliedCount(supplierStatusCountDTO),executorService);

        CompletableFuture<Void> result = CompletableFuture.allOf(all, noRead, noReply, replied);
        result.join();

        SupplierInquiryListTotalVO supplierInquiryListTotalVO = new SupplierInquiryListTotalVO();
        supplierInquiryListTotalVO.setAll(all.get());
        supplierInquiryListTotalVO.setNoRead(noRead.get());
        supplierInquiryListTotalVO.setNoReply(noReply.get());
        supplierInquiryListTotalVO.setReplied(replied.get());
        return Result.success(supplierInquiryListTotalVO);
    }

    @Override
    public Result<SupplierInquiryTotalVO> supplierCount(SupplierStatusCountDTO supplierStatusCountDTO) {
        if (CollectionUtils.isNotEmpty(supplierStatusCountDTO.getBuyerLocationList())) {
            supplierStatusCountDTO.setCountryCodeListFilter(countryCodeDic(supplierStatusCountDTO.getCountryCodeListFilter(),supplierStatusCountDTO.getBuyerLocationList()));
        }
        if(CollectionUtils.isNotEmpty(supplierStatusCountDTO.getRfiSourceList())){
            setRfiSourceList(supplierStatusCountDTO);
        }
        com.globalsources.rfi.agg.response.SupplierInquiryTotalVO supplierInquiryTotalVO = new com.globalsources.rfi.agg.response.SupplierInquiryTotalVO();
        try {
            SupplierStatusCountDTO oneUnReadDTO = new SupplierStatusCountDTO();
            BeanUtil.copyProperties(supplierStatusCountDTO, oneUnReadDTO);
            oneUnReadDTO.setTabType(1);

            CompletableFuture<Integer> oneUnRead = CompletableFuture.supplyAsync(() -> inquirySupplierStatusDao.supplierStatusUnReadCount(oneUnReadDTO),executorService);

            SupplierStatusCountDTO manyUnReadDTO = new SupplierStatusCountDTO();
            BeanUtil.copyProperties(supplierStatusCountDTO, manyUnReadDTO);
            manyUnReadDTO.setTabType(2);

            CompletableFuture<Integer> manyUnRead = CompletableFuture.supplyAsync(() -> inquirySupplierStatusDao.supplierStatusUnReadCount(manyUnReadDTO),executorService);

            SupplierStatusCountDTO starUnReadDTO = new SupplierStatusCountDTO();
            BeanUtil.copyProperties(supplierStatusCountDTO, starUnReadDTO);
            starUnReadDTO.setTabType(3);

            CompletableFuture<Integer> starUnRead = CompletableFuture.supplyAsync(() -> inquirySupplierStatusDao.supplierStatusUnReadCount(starUnReadDTO),executorService);

            // 潜在商机
            SupplierPotentialOpportunityStatusCountDTO potentialOpportunityStateCountDTO = SupplierPotentialOpportunityStatusCountDTO.builder()
                    .supplierId(supplierStatusCountDTO.getSupplierId())
                    .supplierUserId(supplierStatusCountDTO.getSupplierUserId())
                    .supplierAdminFlag(supplierStatusCountDTO.getSupplierAdminFlag())
                    .rfiSourceList(RfiSourceEnum.getNonAdminRfiSource())
                    .build();

            CompletableFuture<SupplierPotentialOpportunityListTotalVO> potentialOpportunity = CompletableFuture.supplyAsync(() -> supplierInquiryService.potentialOpportunityStateCount(potentialOpportunityStateCountDTO), executorService);

            CompletableFuture<Void> all = CompletableFuture.allOf(oneUnRead, manyUnRead, starUnRead, potentialOpportunity);
            all.join();
            supplierInquiryTotalVO.setInquiryNewToOne(oneUnRead.get());
            supplierInquiryTotalVO.setInquiryNewToMany(manyUnRead.get());
            supplierInquiryTotalVO.setStarred(starUnRead.get());


            supplierInquiryTotalVO.setPotentialOpportunity(potentialOpportunity.get().getNoRead());

        } catch (OgnlException e) {
            log.error("supplierCount CompletableFuture get OgnlException:{}", e.getMessage());
        } catch (IntrospectionException e) {
            log.error("supplierCount CompletableFuture get IntrospectionException:{}", e.getMessage());
        } catch (InterruptedException e) {
            log.error("supplierCount CompletableFuture get InterruptedException:{}", e.getMessage());
        } catch (ExecutionException e) {
            log.error("supplierCount CompletableFuture get ExecutionException:{}", e.getMessage());
        }
        return Result.success(supplierInquiryTotalVO);
    }

    private List<String> countryCodeDic(List<String> countryList,List<String> regionIdList) {
        List<String> countryCodeList = CollectionUtils.isEmpty(countryList)?new ArrayList<>() : countryList;
        try {
            for (String regionId : regionIdList) {
                List<DictionaryItemVO> result = dictService.queryDictList(COUNTRY, Long.valueOf(regionId), "en");
                if (CollectionUtils.isNotEmpty(result)) {
                    for (DictionaryItemVO vo : result) {
                        countryCodeList.add(vo.getI18nKey());
                    }
                }
            }
        } catch (Exception e) {
            log.error("set country code filter error:{}", e);
        }
        return countryCodeList;
    }

    private void setCountryCodeList(SupplierInquireDTO supplierInquireDTO) {
        List<String> countryCodeList = CollectionUtils.isEmpty(supplierInquireDTO.getCountryCodeListFilter())?new ArrayList<>() : supplierInquireDTO.getCountryCodeListFilter();
        try {
            for (String regionId : supplierInquireDTO.getBuyerLocationList()) {

                List<DictionaryItemVO> result = dictService.queryDictList(COUNTRY, Long.valueOf(regionId), "en");
                if (CollectionUtils.isNotEmpty(result)) {
                    for (DictionaryItemVO vo : result) {
                        countryCodeList.add(vo.getI18nKey());
                    }
                }
            }
        } catch (Exception e) {
            log.error("set country code filter error:{}", e);
        }
        supplierInquireDTO.setCountryCodeListFilter(countryCodeList);
    }

    @SneakyThrows
    @Override
    public Result<InquiryReplyDetailChatVO> inquiryDetail(UserVO userVO, String inquiryId) {
        //result code,msg
        IResultCode resultCode = null;
        String resultMsg = "";

        InquirySupplierStatusVO inquirySupplierStatusVO = inquiryStateInfo(inquiryId);
        if (ObjectUtils.isEmpty(inquirySupplierStatusVO)) {
            log.error("invoke supplierInquiryFeign.inquiryStateInfo is empty, inquiry = {}", inquiryId);
            return Result.success(new InquiryReplyDetailChatVO());
        }

        //rfi 基本信息
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(inquiryId);

        InquiryBuyerStatusVO buyerInquireStateEntity = buyerInquiryService.inquiryStateInfo(inquiryId);

        if (ObjectUtils.isEmpty(inquireAllVO)) {
            resultCode = ResultCode.RfiResultCode.RFI_FEIGN_INVOKE_EMPTY_CODE;
            resultMsg = ResultCode.RfiResultCode.RFI_FEIGN_INVOKE_EMPTY_CODE.getMsg() + "invoke userFeign getUserProfile and result empty。 supplier = "
                    + inquirySupplierStatusVO.getSupplierId() + ", supplierUserId= " + inquirySupplierStatusVO.getSupplierUserId();
            log.error("invoke inquiryFeign.inquiryAllInfo is empty, inquiry = {}", inquiryId);
            return Result.success(resultCode.getCode(), resultMsg, new InquiryReplyDetailChatVO());
        }

        Long supplierId = inquireAllVO.getInquireAllItemList().getSupplierId();

        //查询所有的回复记录
        List<InquireAllEmailChatVO> chatEntityList = inquiryAllEmailChatDao.supplierEmailChatList(inquiryId,true);

        //最新一条回复记录
        InquiryBuyerDetailVO newInquiryDetailVO = new InquiryBuyerDetailVO();
        //回复记录列表，按时间顺序排序，最新的回复在最后一条。包括第一条回复记录
        List<InquiryBuyerDetailVO> inquiryDetailVOList = new ArrayList<>(chatEntityList.size() + 1);
        List<ProductCategoryAttributeVO> pcAttrVOS = null;
        String desktopProductDetailUrl = "";
        if (!InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType()) && Objects.nonNull(inquireAllVO)) {


            List<InquiryProductCategoryAttrCoreDTO> pcAttrDtoList = inquiryProductCategoryAttrService.getAttrDtoByInquiryId(Objects.nonNull(inquiryId) ? Lists.newArrayList(inquireAllVO.getInquiryId()) : null);
            pcAttrVOS = Optional.ofNullable(pcAttrDtoList).orElse(Lists.newArrayList()).stream().map(pcAttrDto -> {
                ProductCategoryAttributeVO productCategoryAttributeVO = OrikaMapperUtil.coverObject(pcAttrDto, ProductCategoryAttributeVO.class);
                productCategoryAttributeVO.setAttrValue(pcAttrDto.getAttrValues());
                return productCategoryAttributeVO;
            }).collect(Collectors.toList());

            desktopProductDetailUrl = productService.getProductUrl(inquireAllVO.getInquireAllItemList().getProductId());
        }
        ProductDetailVo product = null;
        if (Objects.nonNull(inquireAllVO.getInquireAllItemList()) && Objects.nonNull(inquireAllVO.getInquireAllItemList().getProductId())) {
            product = productService.getProductDetail(inquireAllVO.getInquireAllItemList().getProductId());
        }
        Map<String, List<RfiInquiryEmailAddressCoreVO>> emailAddrMap = getEmailAddressBatch(chatEntityList.stream().filter(Objects::nonNull).map(InquireAllEmailChatVO::getReplyId).collect(Collectors.toList()));
        InquiryBuyerDetailVO entity = null;
        //chatEntityList 回复消息
        for (int i = 0; i < chatEntityList.size(); i++) {
            entity = new InquiryBuyerDetailVO();
            //messageType:1 买家发送的消息messageType:2 卖家发送的消息
            entity.setShowRole(chatEntityList.get(i).getMessageType() == 2);
            /**
             *  对方是否已读
             *  s30需求，假如第三条已读，则前两条都是已读，
             */
            entity.setFlagHaveRead(chatEntityList.get(i).getMessageType() == 2 ? chatEntityList.get(i).getReadFlag() : Boolean.FALSE);
            //发件人的邮箱
            entity.setEmailAddress(chatEntityList.get(i).getSenderEmailAddr());
            //发件人username
            entity.setFirstName(chatEntityList.get(i).getFirstName());
            entity.setLastName(chatEntityList.get(i).getLastName());
            if (i == chatEntityList.size() - 1) {
                entity.setProductCategoryAttrInfos(pcAttrVOS);
                entity.setProductFlag(true);
            }
            //收件人邮箱
            entity.setSendEmailModel(chatEntityList.get(i).getRecipientEmailAddr());
            entity.setRecipientEmailAddr(buyerInquireStateEntity.getEmailAddr());
            entity.setRecipientFirstName(buyerInquireStateEntity.getFirstName());
            entity.setRecipientLastName(buyerInquireStateEntity.getLastName());
            //回复消息
            entity.setMessage(InquiryReplyUtil.replyRepMessage(chatEntityList.get(i).getMessage()));
            entity.setCreateDate(chatEntityList.get(i).getCreateDate());
            entity.setInquiryType(inquireAllVO.getInquiryType().getKey());

            //产品信息
            entity.setAttachmentType(inquireAllVO.getAttachmentType());
            if (!InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
                //增加产品desktopProductDetailUrl
                entity.setDesktopProductDetailUrl(desktopProductDetailUrl);
                //多语言
                if (Objects.nonNull(product) && Objects.nonNull(product.getProductInfoMultiLan())) {
                    entity.setProductNum(inquireAllVO.getInquireAllItemList().getExpectedOrderQty());
                    entity.setProductUnit(product.getProduct().getMinOrderUnit());
                    entity.setProductImage(product.getProductPrimaryImage());
                    entity.setProductTitle(product.getProductInfoMultiLan().getProductName());
                    entity.setModelNumber(product.getProductInfoMultiLan().getModelNumber());
                }
                entity.setProductId(inquireAllVO.getInquireAllItemList().getProductId().toString());
                entity.setAttachmentType("new");
                entity.setSourceName("request");


                //区分new gsol 和 mc附件类型
                if ("old".equals(inquireAllVO.getAttachmentType())) {
                    Boolean flag = inquireAllVO.getInquireAllItemList().getProductImageUrl().indexOf(HOST_NAME) != -1;
                    entity.setSourceName(SourceNameEnum.REQUEST.getKey());
                    if (Boolean.TRUE.equals(flag)) {
                        entity.setAttachmentType("new");
                    }
                }
            }

            // refactor add
            List<InquiryAttachmentVO> attachment = inquiryAllDao.getInquiryAttachmentArrayByReplyId(chatEntityList.get(i).getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(attachment)) {
                attachment = inquiryAllDao.getInquiryFileArrayByReplyId(chatEntityList.get(i).getReplyId());
            }

            if (CollectionUtils.isNotEmpty(attachment)) {
                //s3附件
                entity.setAttachment(fileUpload(attachment, i));
            }

            //抄送邮箱
            List<RfiInquiryEmailAddressCoreVO> rfiInquiryEmailAddressCoreVOS = emailAddrMap.get(chatEntityList.get(i).getReplyId());

            if (CollectionUtils.isNotEmpty(rfiInquiryEmailAddressCoreVOS)) {
                entity.setRfiInquiryEmailAddressList(OrikaMapperUtil.coverList(rfiInquiryEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
            }
            inquiryDetailVOList.add(entity);
        }
        //查询询盘消息

        if (CollectionUtils.isNotEmpty(inquiryDetailVOList)) {
            //按照最早的回复时间重排序，第一条为最早的回复
            inquiryDetailVOList = inquiryDetailVOList.stream().sorted(Comparator.comparing(InquiryBuyerDetailVO::getCreateDate)).collect(Collectors.toList());
            // 获取最新一条回复
            newInquiryDetailVO = inquiryDetailVOList.get(inquiryDetailVOList.size() - 1);
            // 如果是产品询盘则在最新一条显示产品信息
            newInquiryDetailVO.setProductFlag(true);
        }
        //消息已读状态
        InquirySupplierStatusDTO inquirySupplierStatusDTO = new InquirySupplierStatusDTO();
        inquirySupplierStatusDTO.setLUpdDate(new Date());

        Boolean adminFlag = supplierService.hasSupplierRfqAndRfiViewPermissions(userVO);

        if (Boolean.TRUE.equals(adminFlag)) {
            inquirySupplierStatusDTO.setMasterAcctReadFlag(true);
            //如果这个是主账号负责的，同时更新已读状态
            if (userVO.getUserId().equals(inquirySupplierStatusVO.getSupplierUserId())) {
                inquirySupplierStatusDTO.setReadFlag(true);
                /**
                 * s30需求，buyer消息设置为已读
                 */
                inquireAllEmailChatService.markAsRead(inquiryId, 1);
            }
        } else {
            inquirySupplierStatusDTO.setReadFlag(true);
            /**
             * s30需求，buyer消息设置为已读
             */
            inquireAllEmailChatService.markAsRead(inquiryId, 1);
        }
        updateInquiryState(inquirySupplierStatusDTO, inquiryId);

        InquiryCompanyVO inquiryCompanyVO = new InquiryCompanyVO();
        inquiryCompanyVO.setUserId(buyerInquireStateEntity.getBuyerId());
        inquiryCompanyVO.setEmailAddress(buyerInquireStateEntity.getEmailAddr());

        if (Objects.nonNull(inquireAllVO)) {
            newInquiryDetailVO.setInquiryId(inquireAllVO.getInquiryId());
            if (inquireAllVO.getInquireAllItemList().getProductId() != null) {
                newInquiryDetailVO.setProductId(inquireAllVO.getInquireAllItemList().getProductId().toString());
                newInquiryDetailVO.setProductImage(inquireAllVO.getInquireAllItemList().getProductImageUrl());
            }
            String buyerChatOnlineStatus = chatService.getBuyerChatOnlineStatus(inquireAllVO.getBuyerId());
            newInquiryDetailVO.setSupplierId(inquireAllVO.getInquireAllItemList().getSupplierId());
            newInquiryDetailVO.setChatOnlineStatus(buyerChatOnlineStatus);
        }
        Integer upsellReplyCount = 0;
        if (Objects.nonNull(inquireAllVO) && Boolean.TRUE.equals(inquireAllVO.getRecommendFlag())) {
            upsellReplyCount = inquiryAllEmailChatDao.repliedSupplierCount(inquiryId);
        }
        boolean feedbackFlag = Objects.nonNull(inquireAllVO.getInquireAllItemList().getSuppFeedbackDate());

        inquiryDetailVOList = inquiryDetailVOList.stream().sorted(Comparator.comparing(InquiryBuyerDetailVO::getCreateDate)).collect(Collectors.toList());

        Date lastReplyDate = inquiryAllEmailChatDao.buyerNewReply(buyerInquireStateEntity.getBuyerId());

        Date favoriteDate = null;
        try {
            UserFavoriteStatusVO userFavoriteStatusVO = userService.getUserFavoriteStatus(supplierId, buyerInquireStateEntity.getBuyerId());
            if (Objects.nonNull(userFavoriteStatusVO)) {
                favoriteDate = userFavoriteStatusVO.getLUpdDate();
            }
        } catch (Exception exception) {
            log.error("invoke userFavoriteCollectionFeign.getUserFavoriteStatus error, supplierId ={}, buyerId = {}", supplierId, buyerInquireStateEntity.getBuyerId());
        }

        String categoryName = null;
        if (InquiryTypeEnum.CATEGORY.equals(inquireAllVO.getInquiryType()) && "Y".equals(inquireAllVO.getInquireAllItemList().getSupplierType())) {
            try {
                categoryName = productService.getProductDetail(inquireAllVO.getInquireAllItemList().getProductId()).getCategoryInfo().getL4CategoryVo().getCategoryName();
            } catch (Exception e) {
                log.error("get category name exception, threadId:{},productId:{},exception:{}", inquiryId, inquireAllVO.getInquireAllItemList().getProductId(), e.getMessage());
            }
        }
        InquiryReplyDetailChatVO replyChatVO = InquiryReplyDetailChatVO.builder()
                .searchKeywords(inquireAllVO.getQuery())
                .favoriteDate(favoriteDate)
                .inquiryList(inquiryDetailVOList)
                .newInquiry(newInquiryDetailVO)
                .companyModel(inquiryCompanyVO)
                .upsellReplyCount(upsellReplyCount)
                .feedbackFlag(feedbackFlag)
                .chatReplyFlag(buyerInquireStateEntity.getChatReplyFlag())
                .buyerType(inquireAllVO.getBuyerType())
                .countryCode(inquireAllVO.getCountryCode())
                .rfiSource(inquireAllVO.getRfiSource())
                .buyerIp(inquireAllVO.getBuyerIp())
                .lastReplyDate(ObjectUtils.isNotEmpty(lastReplyDate) ? lastReplyDate : null)
                .categoryName(categoryName)
                .subject(inquireAllVO.getSubject())
                .build();
        // 调用common服务，返回此RFI是否被举报标识
        try {
            Boolean complainFlag = dictService.getComplainFlag(inquiryId, supplierId, buyerInquireStateEntity.getBuyerId());
            replyChatVO.setComplainFlag(complainFlag);
        } catch (Exception e) {
            log.info("common service error getting complain flag !");
        }
        try {
            //webSocket通知
            rfiMsgService.sendRfiMsg(RfiMsgVO.builder().supplierId(inquirySupplierStatusVO.getSupplierId()).userId(inquirySupplierStatusVO.getSupplierUserId()).build());
        } catch (Exception exception) {
            log.error("supplier webSocket rfi notice error, supplierId = {}, userId = {}", inquirySupplierStatusVO.getSupplierId(), inquirySupplierStatusVO.getSupplierUserId());
        }

        return Result.success(replyChatVO);

    }

    public boolean updateInquiryState(InquirySupplierStatusDTO inquirySupplierStatusDTO, String inquiryId) {
        log.info("updateInquiryState inquiryRubbish inquiryId:{},supplierInquireStateDTO:{}",inquiryId,inquirySupplierStatusDTO);

        InquirySupplierStatusEntity supplierInquireStateEntity = new InquirySupplierStatusEntity();
        BeanUtils.copyProperties(inquirySupplierStatusDTO, supplierInquireStateEntity, MyBeanUtils.getNullPropertyNames(inquirySupplierStatusDTO));

        return inquirySupplierStatusDao.update(supplierInquireStateEntity, new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, inquiryId))>0;
    }

    @Override
    public boolean inquiryMarkRead(RequestRfiReadDTO requestRfiReadDTO) {
        log.info("SupplierInquiryController inquiryMarkRead requestRfiReadDTO:{}",requestRfiReadDTO);

        Boolean flag = requestRfiReadDTO.getReadFlag() == 1;
        InquirySupplierStatusEntity entity = InquirySupplierStatusEntity.builder()
                .readFlag(flag).build();
        return inquirySupplierStatusDao.update(entity, new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .in(InquirySupplierStatusEntity:: getThreadId, requestRfiReadDTO.getThreadId()).eq(InquirySupplierStatusEntity::getSupplierUserId, requestRfiReadDTO.getUserId()))>0;
    }

    @Override
    public boolean mainMarkRead(RequestRfiReadDTO requestRfiReadDTO) {
        log.info("MainSupplierController mainMarkRead dto:{}",requestRfiReadDTO);
        Boolean flag = requestRfiReadDTO.getReadFlag() == 1;
        InquirySupplierStatusEntity entity = InquirySupplierStatusEntity.builder()
                .masterAcctReadFlag(flag).build();

        return inquirySupplierStatusDao.update(entity,new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .in(InquirySupplierStatusEntity:: getThreadId,requestRfiReadDTO.getThreadId()))>0;
    }

    @Override
    public boolean inquiryStarred(List<String> inquiryIdList, Long userId, Integer starredFlag) {
        Boolean flag = starredFlag == 1;
        InquirySupplierStatusEntity entity = InquirySupplierStatusEntity.builder()
                .starFlag(flag).build();
        return inquirySupplierStatusDao.update(entity, new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .in(InquirySupplierStatusEntity:: getThreadId, inquiryIdList).eq(InquirySupplierStatusEntity::getSupplierUserId, userId))>0;
    }

    @Override
    public boolean mainInquiryStarred(List<String> threadIdList, Long userId, Integer starredFlag) {
        log.info("master supplier starred inquiryIds= {}, userId or supplierId = {}, starredFlag = {}", threadIdList, userId, starredFlag);
        //userId 供应商公司
        Boolean flag = starredFlag == 1;
        InquirySupplierStatusEntity entity = InquirySupplierStatusEntity.builder()
                .masterAcctStarredFlag(flag).build();
        inquirySupplierStatusDao.update(entity,new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .in(InquirySupplierStatusEntity:: getThreadId,threadIdList));
        return false;
    }

    @SneakyThrows
    @Override
    public SupplierInquiryDetailVO inquiryDetailV2(InquiryDetailDTO inquiryDetailDTO) {
        log.info("inquiryDetailV2 dto:{}", inquiryDetailDTO);

        InquirySupplierStatusVO inquirySupplierStatusVO = inquiryStateInfo(inquiryDetailDTO.getInquiryId());
        if (Objects.isNull(inquirySupplierStatusVO)) {
            log.error("inquiryDetailV2 inquiry supplier status is null, inquiryId = {},dto:{}", inquiryDetailDTO.getInquiryId(), inquiryDetailDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        //rfi 基本信息
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(inquiryDetailDTO.getInquiryId());
        if (Objects.isNull(inquireAllVO)) {
            log.error("inquiryDetailV2 inquiry is null, inquiryId ={} ,dto:{}", inquiryDetailDTO.getInquiryId(), inquiryDetailDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        if (Objects.nonNull(inquiryDetailDTO.getSupplierId()) && !inquirySupplierStatusVO.getSupplierId().equals(inquiryDetailDTO.getSupplierId())) {
            log.error("inquiryDetailV2 inquiry view limit - supplier, inquiryId ={} ,dto:{}", inquiryDetailDTO.getInquiryId(), inquiryDetailDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }

        if(Boolean.FALSE.equals(inquiryDetailDTO.getAdminFlag()) && !inquirySupplierStatusVO.getSupplierUserId().equals(inquiryDetailDTO.getSupplierUserId())){
            log.error("inquiryDetailV2 inquiry view limit, inquiryId ={} ,dto:{}", inquiryDetailDTO.getInquiryId(), inquiryDetailDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }

        InquiryBuyerStatusVO inquiryBuyerStatusVO = buyerInquiryService.inquiryStateInfo(inquiryDetailDTO.getInquiryId());

        //产品相关信息
        InquiryProductInfo inquiryProductInfo = getInquiryProductInfo(inquireAllVO);

        Integer upsellReplyCount = 0;
        if (Boolean.TRUE.equals(inquireAllVO.getRecommendFlag())) {
            upsellReplyCount = inquiryAllEmailChatDao.repliedSupplierCount(inquiryDetailDTO.getInquiryId());
        }

        boolean feedbackFlag = Objects.nonNull(inquireAllVO.getInquireAllItemList().getSuppFeedbackDate());

        String categoryName = null;
        if (InquiryTypeEnum.CATEGORY.equals(inquireAllVO.getInquiryType()) && "Y".equals(inquireAllVO.getInquireAllItemList().getSupplierType()) && Objects.nonNull(inquiryProductInfo)) {
            categoryName = inquiryProductInfo.getCategoryName();
        }

        //update status
        updateStatus(inquirySupplierStatusVO, inquiryDetailDTO);
        boolean chatReplyFlag = inquiryBuyerStatusVO.getChatReplyFlag() || inquirySupplierStatusVO.getChatReplyFlag();
        return SupplierInquiryDetailVO.builder()
                .productInfo(inquiryProductInfo)
                .searchKeywords(inquireAllVO.getQuery())
//                .messageList(messageList)
                .upsellReplyCount(upsellReplyCount)
                .feedbackFlag(feedbackFlag)
                .chatReplyFlag(chatReplyFlag)
                .categoryName(categoryName)
                .subject(inquireAllVO.getSubject())
                .inquiryType(inquireAllVO.getInquiryType().getKey())
                .replyEmailAddr(inquiryBuyerStatusVO.getEmailAddr())
                .replyFirstName(inquiryBuyerStatusVO.getFirstName())
                .replyLastName(inquiryBuyerStatusVO.getLastName())
                /**
                 * （1）发送来源为Admin Console，且RFI Type = Tradeshow的RFI，则询盘来源为“展会”，英文为“Tradeshow”
                 *
                 * （2）发送来源为Admin Console，且RFI Type = Component Zone的RFI，询盘来源为“展会”，英文为“Tradeshow”
                 *
                 * （3）发送来源为Admin Console，且RFI Type = Client Service的RFI，询盘来源为“其他”，英文为“Others”
                 */
                .adminTradeshowFlag(RfiSourceEnum.isAdminTradeshowSource(inquireAllVO.getRfiSource()))
                .potentialOpportunityFlag(inquireAllVO.getInquireAllItemList().getPotentialOpportunityFlag())
                .potentialOpportunityConvertFlag(inquireAllVO.getInquireAllItemList().getPotentialOpportunityConvertFlag())
                .build();
    }

    private InquiryProductInfo getInquiryProductInfo(InquireAllVO inquireAllVO) {

        InquiryProductInfo productInfo = null;
        try {
            ProductLiteVO product = null;
            if (Objects.nonNull(inquireAllVO.getInquireAllItemList()) && Objects.nonNull(inquireAllVO.getInquireAllItemList().getProductId()) && !InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
                product = productService.getLiteProductById(inquireAllVO.getInquireAllItemList().getProductId());
            }
            if (Objects.isNull(product)) {
                product = new ProductLiteVO();
            }
            productInfo = OrikaMapperUtil.coverObject(product, InquiryProductInfo.class);
            productInfo.setProductNum(inquireAllVO.getInquireAllItemList().getExpectedOrderQty());
            productInfo.setProductCategoryAttrInfos(getProductCategoryAttrList(inquireAllVO.getInquiryType(), inquireAllVO.getInquiryId()));
            productInfo.setProductId(inquireAllVO.getInquireAllItemList().getProductId());
            productInfo.setProductPrimaryImage(inquireAllVO.getInquireAllItemList().getProductImageUrl());
            productInfo.setProductName(inquireAllVO.getInquireAllItemList().getProductName());
            productInfo.setMinOrderUnit(inquireAllVO.getInquireAllItemList().getExpectedOrderQtyUom());
            productInfo.setModelNumber(inquireAllVO.getInquireAllItemList().getModelNumber());
        } catch (Exception e) {
            log.error("get supplier inquiry detail product info error,param:{},error:{}", inquireAllVO, JSON.toJSONString(e));
        }
        return productInfo;
    }

    @Override
    public InquiryBuyerInfo getInquiryBuyerInfo(String inquiryId) {
        //rfi 基本信息
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(inquiryId);
        if (Objects.isNull(inquireAllVO)) {
            log.error("getInquiryBuyerInfo inquiry is null, inquiryId ={}", inquiryId);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_FEIGN_INVOKE_EMPTY_CODE.getCode(), ResultCode.RfiResultCode.RFI_FEIGN_INVOKE_EMPTY_CODE.getMsg());
        }
        InquiryBuyerInfo buyerInfo = new InquiryBuyerInfo();
        buyerInfo.setUserId(inquireAllVO.getBuyerId());
        buyerInfo.setBuyerType(inquireAllVO.getBuyerType());
        buyerInfo.setIpAddress(inquireAllVO.getBuyerIp());
        buyerInfo.setRfiSource(inquireAllVO.getRfiSource());
        buyerInfo.setLastReplyDate(inquiryAllEmailChatDao.buyerNewReply(inquireAllVO.getBuyerId()));
        buyerInfo.setCountryCode(inquireAllVO.getCountryCode());
        return buyerInfo;
    }

    @Override
    public PageResult<InquiryMessageVO> getInquiryMessagePageList(InquiryMessageListDTO dto) {
        InquirySupplierStatusVO inquirySupplierStatusVO = inquiryStateInfo(dto.getInquiryId());
        if (Objects.isNull(inquirySupplierStatusVO)) {
            log.error("getInquiryMessagePageList inquiry supplier status is null, inquiryId = {},dto:{}", dto.getInquiryId(), dto);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        if (Objects.nonNull(dto.getSupplierId()) && !inquirySupplierStatusVO.getSupplierId().equals(dto.getSupplierId())) {
            log.error("getInquiryMessagePageList inquiry view limit - supplier, inquiryId ={} ,dto:{}", dto.getInquiryId(), dto);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }

        if(Boolean.FALSE.equals(dto.isAdminFlag()) && !inquirySupplierStatusVO.getSupplierUserId().equals(dto.getUserId())){
            log.error("getInquiryMessagePageList inquiry view limit, inquiryId ={} ,dto:{}", dto.getInquiryId(), dto);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }
        IPage<InquireAllEmailChatVO> pageResult = inquiryAllEmailChatDao.getEmailChatListByThreadId(new Page<>(dto.getPageNum(), dto.getPageSize()), dto.getInquiryId(), true, false);
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(dto.getInquiryId());
        List<InquiryMessageVO> messageList = getMessageList(inquireAllVO, pageResult.getRecords());
        return PageResult.restPage(messageList, new Page<InquiryMessageVO>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal()));
    }

    private List<InquiryMessageVO> getMessageList(InquireAllVO inquireAllVO, List<InquireAllEmailChatVO> chatEntityList) {
        //回复记录列表，按时间顺序排序，最新的回复在最后一条。包括第一条回复记录
        List<InquiryMessageVO> messageList = new ArrayList<>(chatEntityList.size() * 2);

        Map<String, List<RfiInquiryEmailAddressCoreVO>> emailAddrMap = getEmailAddressBatch(chatEntityList.stream().filter(Objects::nonNull).map(InquireAllEmailChatVO::getReplyId).collect(Collectors.toList()));

        InquiryMessageVO message = null;
        for (int i = 0; i < chatEntityList.size(); i++) {

            if (Objects.isNull(chatEntityList.get(i))) {
                continue;
            }
            message = new InquiryMessageVO();
            //messageType:1 买家发送的消息messageType:2 卖家发送的消息
            message.setShowRole(chatEntityList.get(i).getMessageType() == 2);
            /**
             *  对方是否已读
             *  s30需求，假如第三条已读，则前两条都是已读，
             */
            message.setFlagHaveRead(chatEntityList.get(i).getMessageType() == 2 ? chatEntityList.get(i).getReadFlag() : Boolean.FALSE);

            //发件人信息
            message.setFromEmailAddress(chatEntityList.get(i).getSenderEmailAddr());
            message.setFromUserFirstName(chatEntityList.get(i).getFirstName());
            message.setFromUserLastName(chatEntityList.get(i).getLastName());

            //收件人信息
            message.setToEmailAddress(chatEntityList.get(i).getRecipientEmailAddr());

            //回复消息
            message.setMessage(chatEntityList.get(i).getMessage());

            message.setSendDate(chatEntityList.get(i).getCreateDate());

            //产品信息
            message.setAttachmentType(inquireAllVO.getAttachmentType());

            if (!InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
                message.setAttachmentType("new");
                //区分new gsol 和 mc附件类型
                if ("old".equals(inquireAllVO.getAttachmentType())) {
                    Boolean flag = inquireAllVO.getInquireAllItemList().getProductImageUrl().indexOf(HOST_NAME) != -1;
                    if (Boolean.TRUE.equals(flag)) {
                        message.setAttachmentType("new");
                    }
                }
            }

            // refactor add
            List<InquiryAttachmentVO> attachmentList = inquiryAllDao.getInquiryAttachmentArrayByReplyId(chatEntityList.get(i).getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(attachmentList)) {
                attachmentList = inquiryAllDao.getInquiryFileArrayByReplyId(chatEntityList.get(i).getReplyId());
            }

            if (CollectionUtils.isNotEmpty(attachmentList)) {
                //s3附件
                message.setAttachment(fileUpload(attachmentList, i));
            }

            //抄送邮箱
            List<RfiInquiryEmailAddressCoreVO> rfiInquiryEmailAddressCoreVOS = emailAddrMap.get(chatEntityList.get(i).getReplyId());

            if (CollectionUtils.isNotEmpty(rfiInquiryEmailAddressCoreVOS)) {
                message.setCcEmailAddressList(OrikaMapperUtil.coverList(rfiInquiryEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
            }

            messageList.add(message);
        }

        log.info("inquiryDetailV2 inquiryId :{} msg list size:{}", inquireAllVO.getInquireAllItemList().getThreadId(), messageList.size());

        return messageList.stream().sorted(Comparator.comparing(InquiryMessageVO::getSendDate).reversed()).collect(Collectors.toList());
    }

    @SneakyThrows
    private List<ProductCategoryAttributeVO> getProductCategoryAttrList(InquiryTypeEnum inquiryType, String inquiryId) {
        List<ProductCategoryAttributeVO> pcAttrVOS = null;
        if (InquiryTypeEnum.SUPPLIER.equals(inquiryType)) {
            return pcAttrVOS;
        }
        List<InquiryProductCategoryAttrCoreDTO> pcAttrDtoList = inquiryProductCategoryAttrService.getAttrDtoByInquiryId(Objects.nonNull(inquiryId) ? Lists.newArrayList(inquiryId) : null);
        pcAttrVOS = Optional.ofNullable(pcAttrDtoList).orElse(Lists.newArrayList()).stream().map(pcAttrDto -> {
            ProductCategoryAttributeVO productCategoryAttributeVO = OrikaMapperUtil.coverObject(pcAttrDto, ProductCategoryAttributeVO.class);
            productCategoryAttributeVO.setAttrValue(pcAttrDto.getAttrValues());
            return productCategoryAttributeVO;
        }).collect(Collectors.toList());
        return pcAttrVOS;
    }

    private void updateStatus(InquirySupplierStatusVO inquirySupplierStatusVO, InquiryDetailDTO inquiryDetailDTO) {
        //消息已读状态
        InquirySupplierStatusDTO inquirySupplierStatusDTO = new InquirySupplierStatusDTO();
        inquirySupplierStatusDTO.setLUpdDate(new Date());
        if (Boolean.TRUE.equals(inquiryDetailDTO.getAdminFlag())) {
            inquirySupplierStatusDTO.setMasterAcctReadFlag(true);
            //如果这个是主账号负责的，同时更新已读状态
            if (inquiryDetailDTO.getSupplierUserId().equals(inquirySupplierStatusVO.getSupplierUserId())) {
                inquirySupplierStatusDTO.setReadFlag(true);
                /**
                 * s30需求，buyer消息设置为已读
                 */
                inquireAllEmailChatService.markAsRead(inquiryDetailDTO.getInquiryId(), 1);
            }
        } else {
            inquirySupplierStatusDTO.setReadFlag(true);
            /**
             * s30需求，buyer消息设置为已读
             */
            inquireAllEmailChatService.markAsRead(inquiryDetailDTO.getInquiryId(), 1);
        }
        updateInquiryState(inquirySupplierStatusDTO, inquiryDetailDTO.getInquiryId());

        try {
            //webSocket通知
            rfiMsgService.sendRfiMsg(RfiMsgVO.builder().supplierId(inquirySupplierStatusVO.getSupplierId()).userId(inquirySupplierStatusVO.getSupplierUserId()).build());
        } catch (Exception exception) {
            log.error("supplier webSocket rfi notice error, supplierId = {}, userId = {}", inquirySupplierStatusVO.getSupplierId(), inquirySupplierStatusVO.getSupplierUserId());
        }
    }

    private Map<String, List<RfiInquiryEmailAddressCoreVO>> getEmailAddressBatch(List<String> replyIds) {
        if (CollectionUtils.isEmpty(replyIds)) {
            return new HashMap<>();
        }
        Map<String, List<RfiInquiryEmailAddressCoreVO>> addrMap = new HashMap<>(replyIds.size());
        try {
            // refactor message list
            List<RfiInquiryEmailAddressCoreVO> list = inquiryAllEmailService.selectInquireEmailListBatch(Lists.newArrayList(RfiCommonConstants.CC), replyIds);

            // refactor remove 旧查询
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> findIds = list.stream().map(RfiInquiryEmailAddressCoreVO::getReplyId).distinct().collect(Collectors.toList());
                List<String> noFindIds = replyIds.stream().filter(item -> !findIds.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noFindIds)) {
                    List<RfiInquiryEmailAddressCoreVO> addrList = inquiryAllEmailAddressService.emailAddressBatch(noFindIds);
                    if (CollectionUtils.isNotEmpty(addrList)) {
                        list.addAll(addrList);
                    }
                }
            } else {
                list = inquiryAllEmailAddressService.emailAddressBatch(replyIds);
            }

            if (CollectionUtils.isEmpty(list)) {
                return addrMap;
            }
            for (RfiInquiryEmailAddressCoreVO addr : list) {
                if (Objects.nonNull(addr)) {
                    List<RfiInquiryEmailAddressCoreVO> itemList = addrMap.get(addr.getReplyId());
                    if (CollectionUtils.isEmpty(itemList)) {
                        itemList = new ArrayList<>();
                        addrMap.put(addr.getReplyId(), itemList);
                    }
                    itemList.add(addr);
                }
            }
        } catch (Exception e) {
            log.error("getEmailAddressBatch error,param:{},exception:{}", replyIds, JSON.toJSONString(e));
        }
        return addrMap;
    }

    @Override
    public Result inquiryReply(InquiryChatAGGDTO inquiryChatAGGDTO, Boolean offlineFlag) {
        UserVO currentUser = inquiryChatAGGDTO.getUserVO();
        log.info("inquiryReply dto={}", inquiryChatAGGDTO);
        InquiryChatDTO inquiryChatDTO = inquiryChatAGGDTO.getInquiryChatDTO();
        InquireAllVO inquireAllEntity = inquiryAllDao.findOneByThreadId(inquiryChatDTO.getInquiryId());

        if (Objects.isNull(inquireAllEntity)) {
            log.error("inquiryReply error,inquireAllEntity is null,dto:{}{}", inquiryChatAGGDTO, offlineFlag);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        //卖家发送的买家信息
        InquiryBuyerStatusVO buyerInquireStateEntity = buyerInquiryService.inquiryStateInfo(inquiryChatDTO.getInquiryId());
        if (Objects.isNull(buyerInquireStateEntity)) {
            log.error("inquiryReply error,buyerInquireStateEntity is null,dto:{}{}", inquiryChatAGGDTO, offlineFlag);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        InquirySupplierStatusVO inquirySupplierStatusVO = inquiryStateInfo(inquiryChatDTO.getInquiryId());
        Boolean adminFlag = supplierService.hasSupplierRfqAndRfiViewPermissions(currentUser);
        //s71 安全问题fix, 攻击者回复一个不属于自己的rfiId，这里校验输入的rfiId对应的关联关系中supplierId与当前登录用户一致
        if (Objects.isNull(inquirySupplierStatusVO) || !Objects.equals(currentUser.getCurrentSupplier().getSupplierId(),inquirySupplierStatusVO.getSupplierId())) {
            log.error("inquiryReply error,wrong inquiry param ,dto:{}{}", inquiryChatAGGDTO, offlineFlag);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        //如果是离线回复&买家是黑名单，不发送消息,直接返回
        if (offlineFlag && ResultUtil.getData(blackListFeign.isBlackListUser(buyerInquireStateEntity.getBuyerId(), buyerInquireStateEntity.getEmailAddr()))) {
            log.warn("inquiryReply supplier offline reply blacklist buyer:{},rfi:{}", buyerInquireStateEntity.getBuyerId(), inquiryChatDTO.getInquiryId());
            saveErrorLog(inquiryChatDTO, inquiryChatDTO.getInquiryId(), OfflineEmailLogTypeEnum.BLACKLIST_BUYER);
            return Result.success();
        }

        //如果是在线回复，且回复人不是主账号，也不是当前询盘负责人，返回提示
        //这里的提示有问题，现在弹得是buyer端的提示，pd说先不改
        if(Objects.equals(Boolean.FALSE,offlineFlag) && Objects.equals(Boolean.FALSE,adminFlag) && !Objects.equals(inquirySupplierStatusVO.getSupplierUserId(),currentUser.getUserId())){
            log.error("inquiryReply inquiry reply limit, inquiryId ={} ,reply userId:{},inquiryUserId:{} ,dto:{}", inquiryChatDTO.getInquiryId(),currentUser.getUserId(),inquirySupplierStatusVO.getSupplierUserId(), inquiryChatDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_REPLY_LIMIT);
        }

        //买家公司名称
        UserBaseProfileVO userBaseProfileVO = userService.getUserBaseProfile(currentUser.getUserId());
        String buyerCompanyName = userBaseProfileVO.getCompanyInfo().getCompanyName();


        //卖家公司名称
        String supplierCompanyName = supplierService.getSupplierName(inquirySupplierStatusVO.getSupplierId());
        log.info("inquiryReply adminFlag={}", adminFlag);

        Boolean masterReassignFlag = Boolean.FALSE;
        //如果主账号回复，则重新分配
        log.info("supplier reply, adminFlag = {}, supplier status = {}, current userId = {}", adminFlag, inquirySupplierStatusVO.getSupplierUserId(), currentUser.getUserId());
        if (Boolean.TRUE.equals(adminFlag) && !inquirySupplierStatusVO.getSupplierUserId().equals(currentUser.getUserId())) {
            String[] inquiryIds = new String[]{inquiryChatAGGDTO.getInquiryChatDTO().getInquiryId()};
            if (supInqReassign(SupplierReassignDTO.builder().inquiryIds(inquiryIds).supplierId(currentUser.getCurrentSupplier().getSupplierId()).supplierUserId(currentUser.getUserId()).source(inquiryChatAGGDTO.getSource()).build()).getData() != null) {
                masterReassignFlag = Boolean.TRUE;
                inquireAllService.sendReassignEmail(currentUser.getUserId(), currentUser.getUserId(), currentUser.getCurrentSupplier().getSupplierId(), inquiryChatAGGDTO.getInquiryChatDTO().getInquiryId());
            }
        }
        //重新分配后，供应商负责人的名字不一样了，重新读一下inquiry_supplier_status，不然邮件通知会出错
        if(Boolean.TRUE.equals(masterReassignFlag)){
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.setMasterRouteOnly();
                inquirySupplierStatusVO = inquiryStateInfo(inquiryChatDTO.getInquiryId());
            }catch (Exception e){
                log.error("inquiryReply inquiryStateInfo error,inquiryId={},error:{}",inquiryChatDTO.getInquiryId(),JSON.toJSONString(e));
            }
        }
        Snowflake snowflake = IdUtil.createSnowflake(RandomUtils.nextLong(0, 32), RandomUtils.nextLong(0, 32));
        String replyId = String.valueOf(snowflake.nextId());

        UserVO userVO = userService.getUserInfo(buyerInquireStateEntity.getBuyerId());
        if (ObjectUtils.isNotEmpty(inquireAllEntity)) {
            List<AttachmentCoreDTO> attachmentCoreDTOList = new ArrayList<>();
            if (StringUtils.isNotBlank(inquiryChatDTO.getAttachmentList())) {
                //保存记录附件
                log.info("inquiry reply attachment json data = {}", inquiryChatDTO.getAttachmentList());
                List<AttachmentAggDTO> inquiryRFIRedistributePOList = JSON.parseArray(inquiryChatDTO.getAttachmentList(), AttachmentAggDTO.class);
                attachmentCoreDTOList = JSON.parseArray(inquiryChatDTO.getAttachmentList(), AttachmentCoreDTO.class);

                log.info("inquiry reply attachment list data = {}", inquiryRFIRedistributePOList);
                inquireAllFileAttachmentService.saveInquiryFile(inquiryRFIRedistributePOList, replyId, currentUser.getUserId());
            }

            //保存回复多人记录
            if (StringUtils.isNotEmpty(inquiryChatDTO.getTo())) {
                String[] tos = StringUtils.split(inquiryChatDTO.getTo());
                inquiryEmailAddressUtil.saveEmailAddress(inquireAllEntity, tos, "TO", replyId);
            }

            //增加 message信息，保存message附件
            InquireAllEmailChatDTO chatEntity = new InquireAllEmailChatDTO();
            chatEntity.setInquiryId(inquiryChatDTO.getInquiryId());
            chatEntity.setReplyId(replyId);
            chatEntity.setMessage(inquiryChatDTO.getMessage());
            chatEntity.setMessageType(2);

            //发信消息人员
            chatEntity.setSenderUserId(currentUser.getUserId());
            chatEntity.setSenderEmailAddr(currentUser.getEmail());
            chatEntity.setFirstName(currentUser.getFirstName());
            chatEntity.setLastName(currentUser.getLastName());

            //接收消息人员
            chatEntity.setUserId(userVO.getUserId());
            chatEntity.setRecipientEmailAddr(userVO.getEmail());
            chatEntity.setSupplierId(currentUser.getCurrentSupplier().getSupplierId());

            chatEntity.setAttachmentCoreDTOList(attachmentCoreDTOList);
            chatEntity.setSupplierType(1);
            chatEntity.setOfflineFlag(offlineFlag);
            chatEntity.setReplySource(inquiryChatAGGDTO.getSource());
            inquireAllEmailChatService.addInquiryEmailChat(chatEntity);
            log.info("inquiryReply addInquiryEmailChat succ,entity:{}", chatEntity);

            //消息模板
            String templateId = RfxEmailTemplateEnum.RFI_PROD_BUYER_REPLY.getKey();
            //发送通知
            InquiryReplyDetailVO inquiryReplyDetailVO = new InquiryReplyDetailVO();
            inquiryReplyDetailVO.setInquiryId(inquiryChatDTO.getInquiryId());
            inquiryReplyDetailVO.setSubmitDate(inquireAllEntity.getCreateDate());
            //发送方的姓名
            inquiryReplyDetailVO.setFirstName(currentUser.getFirstName());
            inquiryReplyDetailVO.setLastName(currentUser.getLastName());
            inquiryReplyDetailVO.setUserId(buyerInquireStateEntity.getBuyerId());
            inquiryReplyDetailVO.setOrgId(userVO.getCurrentSupplier().getSupplierId());

            //客户服务经理
            OrganizationDTO supplierInfoResult = supplierService.getSupplierNameAndCsoUserId(inquirySupplierStatusVO.getSupplierId());
            if (Objects.nonNull(supplierInfoResult)) {
                UserVO gsolUserVO = userService.getUserInfo(supplierInfoResult.getCsoUserId());
                if(Objects.nonNull(gsolUserVO) && Objects.nonNull(gsolUserVO.getUserId())){
                    inquiryReplyDetailVO.setGSOLEmail(gsolUserVO.getEmail());
                    inquiryReplyDetailVO.setGSOLFirstName(gsolUserVO.getFirstName());
                    inquiryReplyDetailVO.setGSOLLastName(gsolUserVO.getLastName());
                }else{
                    inquiryReplyDetailVO.setGSOLEmail(DEFAULT_CSO_EMAIL);
                    inquiryReplyDetailVO.setGSOLFirstName(DEFAULT_CSO_USER_FIRST_NAME);
                    inquiryReplyDetailVO.setGSOLLastName(DEFAULT_CSO_USER_LAST_NAME);
                }
            }
            //假如用户没改标题，前端传的null，这里需要查库，确保邮件标题不为空
            inquiryReplyDetailVO.setSubject(StringUtils.isEmpty(inquiryChatDTO.getSubject()) ? inquiryAllItemDao.selectSubjectByThreadId(inquiryChatDTO.getInquiryId()) : inquiryChatDTO.getSubject());

            //判断询盘类型，填充产品信息
            if (!InquiryTypeEnum.SUPPLIER.getKey().equals(inquireAllEntity.getInquiryType().getKey())) {
                InquiryProductVO inquiryProductVO = new InquiryProductVO();
                inquiryProductVO.setProductId(inquireAllEntity.getInquireAllItemList().getProductId().toString());
                inquiryProductVO.setProductImage(inquireAllEntity.getInquireAllItemList().getProductImageUrl());
                inquiryProductVO.setProductUnit(inquireAllEntity.getInquireAllItemList().getExpectedOrderQtyUom());
                inquiryProductVO.setProductNum(inquireAllEntity.getInquireAllItemList().getExpectedOrderQty());
                inquiryProductVO.setProductTitle(inquireAllEntity.getInquireAllItemList().getProductName());
                inquiryProductVO.setModelNumber(inquireAllEntity.getInquireAllItemList().getModelNumber());
                inquiryReplyDetailVO.setInquiryProduct(inquiryProductVO);
            }
            //这里强制读主库，避免刚插入的数据读不到
            List<InquireAllEmailChatVO> chatEntityList = new ArrayList<>();
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.setMasterRouteOnly();
                chatEntityList = inquiryAllEmailChatDao.inquiryAllEmailChatList(inquiryChatDTO.getInquiryId(),false);
            }catch (Exception e){
                log.error("inquiryReply inquiryAllEmailChatList error,inquiryId={},error:{}",inquiryChatDTO.getInquiryId(),JSON.toJSONString(e));
            }
            String message = "";
            String newReplyMessageSendTimeStr = LocalDateUtils.dateToYYDDMM(inquirySupplierStatusVO.getCreateDate());
            if (CollectionUtils.isNotEmpty(chatEntityList)) {
                if (Objects.nonNull(chatEntityList.get(0))) {
                    message = chatEntityList.get(0).getMessage();
                    newReplyMessageSendTimeStr = LocalDateUtils.dateToYYDDMM(chatEntityList.get(0).getCreateDate());
                }
            } else {
                message = inquireAllEntity.getBuyerMessage();
            }
            //回复信息
            inquiryReplyDetailVO.setNewInquiryReply(InquiryReplyVO.builder()
                    .firstName(inquirySupplierStatusVO.getFirstName())
                    .lastName(inquirySupplierStatusVO.getLastName())
                    .message(message)
                    .companyName(supplierCompanyName)
                    .createDate(newReplyMessageSendTimeStr)
                    .build());
            //接收方的userId
            if (inquireAllEntity.getInquiryType().getKey().equals(InquiryTypeEnum.SUPPLIER.getKey())) {
                templateId = RfxEmailTemplateEnum.RFI_SUPPLIER_BUYER_REPLY.getKey();
            }

            //回复记录
            List<InquiryReplyVO> inquiryReplyVOList = new ArrayList<>();
            for (int i = 0; i < chatEntityList.size(); i++) {
                if (i < 3) {
                    InquiryReplyVO inquiryReplyVO = InquiryReplyVO.builder()
                            .firstName(chatEntityList.get(i).getFirstName())
                            .lastName(chatEntityList.get(i).getLastName())
                            .message(chatEntityList.get(i).getMessage())
                            .photo(chatEntityList.get(i).getMessageType() == 1 ? userVO.getPhoto() : currentUser.getPhoto())
                            .companyName(chatEntityList.get(i).getMessageType() == 1 ? buyerCompanyName : supplierCompanyName)
                            .createDate(LocalDateUtils.timestampToYYDDMM(chatEntityList.get(i).getCreateDate())).build();
                    inquiryReplyVOList.add(inquiryReplyVO);
                }

                List<InquiryReplyVO> compareSortList = inquiryReplyVOList.stream().sorted(Comparator.comparing(InquiryReplyVO::getCreateDate)).collect(Collectors.toList());
                inquiryReplyDetailVO.setInquiryReplyVOS(compareSortList);
            }
            //查询询盘对应编号，查询出用户，根据用户获取到发送邮箱
            HashMap<String, Object> templateParams = new HashMap<>(2);
            //s75,首次回复标记
            inquiryReplyDetailVO.setInquirySupplierFirstReplyFlag(chatEntityList.stream().filter(Objects::nonNull).filter(c -> Objects.equals(c.getMessageType(), 2)).count() <=1);
            templateParams.put(templateId, JSON.toJSONString(inquiryReplyDetailVO));

            //更新未读状态，同时是待回复状态
            InquirySupplierStatusDTO supplierInquireStateDTO = InquirySupplierStatusDTO.builder().readFlag(true).distFlag(true).replyFlag(true).build();
            updateInquiryState(supplierInquireStateDTO, inquiryChatDTO.getInquiryId());
            log.info("inquiryReply supplierInquiryFeign.updateInquiryState succ,entity:{}", supplierInquireStateDTO);

            //买家更新已回复
            InquiryBuyerStatusDTO buyerInquireStateDTO = InquiryBuyerStatusDTO.builder().readFlag(false).replyFlag(false).build();
            buyerInquiryService.updateInquiryState(buyerInquireStateDTO, inquiryChatDTO.getInquiryId());
            log.info("inquiryReply buyerInquiryFeign.updateInquiryState succ,entity:{}", buyerInquireStateDTO);


            /**
             * s30需求，buyer消息设置为已读
             */
            inquireAllEmailChatService.markAsRead(inquiryChatDTO.getInquiryId(), 1);
            log.info("inquiryReply markAsRead succ,InquiryId:{}", inquiryChatDTO.getInquiryId());

            /**
             * s31,修改标题
             */
            if (StringUtils.isNotEmpty(inquiryChatDTO.getSubject())) {
                inquiryAllItemService.updateSubjectByThreadId(inquiryChatDTO.getInquiryId(), inquiryChatDTO.getSubject());
                log.info("inquiryReply updateSubjectByThreadId succ,InquiryId:{},subject:{}", inquiryChatDTO.getInquiryId(),inquiryChatDTO.getSubject());
            }

            /**
             * s69， 潜在商机，如果回复的是潜在商机询盘，且未打标记，则标记potentialOpportunityConvertFlag=true
             */
            if (Objects.equals(Boolean.TRUE,inquireAllEntity.getInquireAllItemList().getPotentialOpportunityFlag()) && Objects.equals(Boolean.FALSE,inquireAllEntity.getInquireAllItemList().getPotentialOpportunityConvertFlag())) {
                inquiryAllItemService.markPotentialOpportunityConvert(Collections.singletonList(inquiryChatDTO.getInquiryId()),inquiryChatAGGDTO.getSource());
                commonService.syncPscCmAndScPoint(Collections.singletonList(inquiryAllItemDao.selectById(inquireAllEntity.getThreadId())));
                commonService.sensorData(Collections.singletonList(inquireAllEntity.getThreadId()));
                log.info("inquiryReply potentialOpportunityConvertFlag succ,InquiryId:{}", inquiryChatDTO.getInquiryId());
            }

            //记录状态 @InquiryStatus(status = "reply")
            InquiryStatusDTO statusDTO = InquiryStatusDTO.builder()
                    .inquiryId(inquireAllEntity.getInquiryId())
                    .threadId(inquiryChatDTO.getInquiryId())
                    .userId(currentUser.getUserId())
                    .status(InquiryStatusEnum.REPLY.getStatus())
                    .reviewType("")
                    .email(currentUser.getEmail())
                    .upsellFlag(inquireAllEntity.getRecommendFlag())
                    .build();
            inquiryStatusService.saveInquiryStatus(statusDTO);
            log.info("inquiryReply saveInquiryStatus succ,dto:{}", statusDTO);


                String fromEmail = inquireAllService.getMessageEmail(currentUser.getUserId(), currentUser.getCurrentSupplier().getSupplierId(),
                        currentUser.getFirstName(), currentUser.getLastName(), currentUser.getEmail());

                // cc 里有message用户真实邮箱  不发送邮件
                if (!getSendMailFlag(inquiryChatDTO.getCc(), userVO.getEmail()) && Boolean.TRUE.equals(offlineFlag)) {
                    log.info("offline sync cc contains to, threadId:{},ccString:{},to:{}", inquiryChatDTO.getInquiryId(), null != inquiryChatDTO.getCc() ? JSON.toJSONString(inquiryChatDTO.getCc()) : StringUtils.EMPTY, userVO.getEmail());
                    return Result.success();
                }
                String cc = null;
                try {
                    //非离线回复且cc有数据
                    if (Boolean.FALSE.equals(offlineFlag) && Objects.nonNull(inquiryChatDTO.getCc()) && inquiryChatDTO.getCc().length > 0) {
                        cc = String.join(",", Arrays.asList(inquiryChatDTO.getCc()));
                    }
                } catch (Exception e) {
                    log.error("supplier reply get cc error, dto:{},error:{}", inquiryChatDTO, JSON.toJSONString(e));
                }
                inquiryEmailAddressUtil.saveEmailAddress(inquireAllEntity, inquiryChatDTO.getCc(), "CC", replyId);
            try {
                /**
                 * 构建email参数  去除CC（邮件客户端已发送给CC用户）
                 */
                EmailModelPO emailModelPO = EmailModelPO.builder()
                        .templateParams(templateParams)
                        .templateId(templateId)
                        .form(fromEmail)
                        .to(userVO.getEmail())
                        .cc(cc)
                        .build();
                rabbitService.sendMsg(emailModelPO);
                log.info("inquiryReply send email succ,dto:{}", emailModelPO);
            } catch (Exception exception) {
                log.warn("send email fail, inquiryId:{},error:{}", inquireAllEntity.getInquiryId(), JSON.toJSONString(exception));
            }

            return Result.success();
        }
        //保存邮箱发送记录
        return Result.failed("不存在的询盘!");
    }

    private void saveErrorLog(InquiryChatDTO inquiryChatDTO, String inquiryId, OfflineEmailLogTypeEnum enums) {
        try{
            inquiryLogDao.insert(OrikaMapperUtil.coverObject(InquiryLogSaveDTO.builder()
                    .createDate(new Date())
                    .businessId(inquiryId)
                    .businessType(InquiryLogEnum.OFFLINE_EMAIL.getKey())
                    .logCode(enums.getKey())
                    .logData(enums.getValue())
                    .requestData(JSON.toJSONString(inquiryChatDTO))
                    .build(), InquiryLogEntity.class));
        }catch (Exception e){
            log.error("saveErrorLog inquiryId:{},enums:{},exception:{}",inquiryId,enums,JSON.toJSONString(e));
        }
    }
    /**
     * 是否包含email
     *
     * @param cc         ccEmail
     * @param buyerEmail buyerEmail
     * @return boolean
     */
    private boolean getSendMailFlag(String[] cc, String buyerEmail) {
        boolean result = true;
        if (null != cc) {
            for (String s : cc) {
                if (s.equals(buyerEmail)) {
                    result = false;
                    break;
                }
            }
        }
        return result;
    }

    @SneakyThrows
    @Override
    public Result<APPInquiryReplyChatVO> appInquiryDetail(String inquiryId, UserVO userVO) {
        log.info("supplierAppInquiryDetail inquiryId :{}, userVO : {}", inquiryId,userVO);

        if (Objects.isNull(userVO.getCurrentSupplier()) || Objects.isNull(userVO.getCurrentSupplier().getSupplierId())) {
            throw new BusinessException(ResultCode.UserResultCode.NOT_SWITCH_SUPPLIER);
        }
        InquirySupplierStatusVO supplierInquireStateEntity = inquiryStateInfo(inquiryId);

        if (Objects.isNull(supplierInquireStateEntity)) {
            log.error("supplierAppInquiryDetail inquiry supplier status is null, inquiryId = {},userVO :{}", inquiryId, userVO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        //rfi 基本信息
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(inquiryId);
        if (Objects.isNull(inquireAllVO)) {
            log.error("supplierAppInquiryDetail inquiry is null, inquiryId ={} ,userVO:{}", inquiryId, userVO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        Boolean adminFlag = supplierService.hasSupplierRfqAndRfiViewPermissions(userVO);

        if(!Objects.equals(supplierInquireStateEntity.getSupplierId(),userVO.getCurrentSupplier().getSupplierId())){
            log.error("supplierAppInquiryDetail inquiry view limit, inquiryId ={} ,userVO:{}", inquiryId, userVO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }

        if(Boolean.FALSE.equals(adminFlag) && !supplierInquireStateEntity.getSupplierUserId().equals(userVO.getUserId())){
            log.error("supplierAppInquiryDetail inquiry view limit, inquiryId ={} ,userVO:{}", inquiryId, userVO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }

        Long buyerId = inquireAllVO.getBuyerId();
        UserVO buyerUserVO = null;
        if (Objects.isNull(buyerId)) {
            log.warn("supplierAppInquiryDetail buyerId is null, inquiryId : {}", inquiryId);
        } else {
            buyerUserVO = userService.getUserInfo(buyerId);
            if (Objects.isNull(buyerUserVO)) {
                log.warn("supplierAppInquiryDetail buyer is null, inquiryId : {} buyerId :{}", inquiryId,buyerId);
            }
        }
        buyerUserVO = Optional.ofNullable(buyerUserVO).orElse(new UserVO());

        String buyerFirstName = inquireAllVO.getFirstName();
        String buyerLastName = inquireAllVO.getLastName();
        if (ObjectUtils.isEmpty(supplierInquireStateEntity)) {
            return Result.success(new APPInquiryReplyChatVO());
        }
        //查询询盘消息
        InquiryDetailVO inquiryDetailVO = inquiryAllEmailChatDao.getInquiryDetail(inquiryId);
        if (ObjectUtils.isEmpty(inquiryDetailVO)) {
            return Result.success(new APPInquiryReplyChatVO());
        }

        // refactor add supplier app询盘详情
        List<InquiryAttachmentVO> attachmentVOList = inquiryAllDao.getInquiryAttachmentList(inquiryId);

        // refactor remove 旧查询（通过threadId查询）
        if(CollectionUtils.isNotEmpty(attachmentVOList)) {
            List<String> findIds = attachmentVOList.stream().filter(Objects::nonNull).map(InquiryAttachmentVO::getFileId).collect(Collectors.toList());

            List<InquiryAttachmentVO> attachmentList = inquiryAllDao.getInquiryFileList(inquiryId, findIds);
            if(CollectionUtils.isNotEmpty(attachmentList)){
                attachmentVOList.addAll(attachmentList);
            }
            log.info("appInquiryDetail, inquiryId:{}, findIds size:{}, attachmentVOList size:{}", inquiryId, findIds.size(), attachmentVOList.size());
        } else{
            attachmentVOList = inquiryAllDao.getInquiryFileList(inquiryId, null);
            log.info("appInquiryDetail, inquiryId:{}, attachmentVOList size:{}", inquiryId, attachmentVOList.size());
        }


        if (CollectionUtils.isNotEmpty(attachmentVOList)) {
            inquiryDetailVO.setAttachment(attachmentVOList);
        }

        UserBaseProfileVO buyerUserProfileVO = userService.getUserBaseProfile(inquireAllVO.getBuyerId());
        if (ObjectUtils.isEmpty(buyerUserProfileVO)) {
            return Result.success(new APPInquiryReplyChatVO());
        }
        APPInquiryReplyChatVO appInquiryReplyChatVO = new APPInquiryReplyChatVO();
        appInquiryReplyChatVO.setThreadId(inquiryId);
        appInquiryReplyChatVO.setDeleteFlag(supplierInquireStateEntity.getMarkDelFlag());
        appInquiryReplyChatVO.setBuyerId(inquireAllVO.getBuyerId());
        appInquiryReplyChatVO.setVipFlag(buyerUserProfileVO.getContactInfo().getVipFlag() == null ? Boolean.FALSE : buyerUserProfileVO.getContactInfo().getVipFlag());
        appInquiryReplyChatVO.setVerifiedFlag(buyerUserProfileVO.getContactInfo().getVerifiedFlag()  == null ? Boolean.FALSE : buyerUserProfileVO.getContactInfo().getVerifiedFlag());
        appInquiryReplyChatVO.setReplyFlag(Optional.ofNullable(supplierInquireStateEntity.getReplyFlag()).orElse(Boolean.FALSE));
        inquiryDetailVO.setEmailAddress(supplierInquireStateEntity.getEmailAddr());
        inquiryDetailVO.setMessage(InquiryReplyUtil.replyRepMessage(inquiryDetailVO.getMessage()));
        //TODO 收件 第一条 .  取不到第一次分配的收人人
        inquiryDetailVO.setFirstName(supplierInquireStateEntity.getFirstName());
        inquiryDetailVO.setLastName(supplierInquireStateEntity.getLastName());
        inquiryDetailVO.setSenderEmail(buyerUserVO.getEmail());

        APPInquiryDetailVO result = new APPInquiryDetailVO();
        BeanUtils.copyProperties(inquiryDetailVO, result);
        //发件人
        result.setSenderFirstName(buyerFirstName);
        result.setSenderLastName(buyerLastName);
        result.setCreateDate(inquiryDetailVO.getCreateDate());
        //取买家国家
        result.setCountry(dictCountryUtils.convertMapForOrderStatus(inquireAllVO.getCountryCode()));
        result.setOwnerFirstName(supplierInquireStateEntity.getFirstName());
        result.setOwnerLastName(supplierInquireStateEntity.getLastName());
        result.setInquiryType(InquiryTypeEnum.getEnumByKey(inquiryDetailVO.getInquiryType().getKey()));
        //保持字段统一，第一条消息是buyer发的，buyer不需要已读
        result.setFlagHaveRead(Boolean.FALSE);
        if (!InquiryTypeEnum.SUPPLIER.equals(inquiryDetailVO.getInquiryType())) {
            String productId = inquiryDetailVO.getProductId();
            String productCategoryName = getCategoryNameByProductId(productId);
            InquiryProdVO inquiryProdVO = InquiryProdVO.builder()
                    .productId(productId)
                    .productImage(inquiryDetailVO.getProductImage())
                    .productName(inquiryDetailVO.getProductTitle())
                    .productNum(inquiryDetailVO.getProductNum())
                    .productUnit(inquiryDetailVO.getProductUnit())
                    .modelNumber(Optional.ofNullable(inquiryDetailVO.getModelNumber()).orElse(StringUtils.EMPTY))
                    .categoryName(productCategoryName)
                    .productCategoryAttrInfos(getProductCategoryAttrList(inquireAllVO.getInquiryType(), inquireAllVO.getInquiryId()))
                    .build();
            result.setProducts(Objects.nonNull(inquiryProdVO) ? Collections.singletonList(inquiryProdVO) : Lists.newArrayList());
        }
        appInquiryReplyChatVO.setInquiryDetailVO(result);
        //查询所有的回复记录
        List<InquireAllEmailChatVO> chatEntityList = inquiryAllEmailChatDao.inquiryAllEmailChatList(inquiryId,false);
        //需要放入第一条消息，确保list非空
        chatEntityList = Optional.ofNullable(chatEntityList).orElse(Lists.newArrayList());

        List<APPInquiryDetailVO> inquiryDetailVOList = new ArrayList<>(chatEntityList.size());
        for (int i = 0; i < chatEntityList.size(); i++) {
            InquireAllEmailChatVO inquireAllEmailChatVO = chatEntityList.get(i);
            APPInquiryDetailVO entity = new APPInquiryDetailVO();
            entity.setFlagHaveRead(inquireAllEmailChatVO.getMessageType() == 2 ? inquireAllEmailChatVO.getReadFlag() : Boolean.FALSE);

            //type: 1:buyer回复, 2:supplier回复
            if (InquiryCoreConstants.InquireChatMessageType.BUYER.getKey().equals(inquireAllEmailChatVO.getMessageType())) {
                if (Optional.ofNullable(inquireAllEmailChatVO.getSenderEmailAddr()).orElse(StringUtils.EMPTY).equals(userVO.getEmail())) {
                    //收件人是当前供应商user， 收件人 姓名
                    entity.setFirstName(userVO.getFirstName());
                    entity.setLastName(userVO.getLastName());
                } else {
                    //TODO 收件人非当前供应商用户 要建表，暂使用当前分配的supplier user name
                    entity.setFirstName(supplierInquireStateEntity.getFirstName());
                    entity.setLastName(supplierInquireStateEntity.getLastName());
                }

                //类型为买家回复 没有发件人邮箱 inquireAllEmailChat 的 email_addr (发件人邮箱) 没有数据 从buyerUserVO取
                entity.setSenderEmail(buyerUserVO.getEmail());

            } else {
                //卖家回复 收件人
                entity.setFirstName(buyerFirstName);
                entity.setLastName(buyerLastName);

                //发件人的邮箱(卖家) 类型为供应商回复 inquireAllEmailChat 的 email_addr才有数据
                entity.setSenderEmail(inquireAllEmailChatVO.getSenderEmailAddr());
            }

            //收件人的邮箱 表里的sendEmail 指收件人Email
            entity.setEmailAddress(inquireAllEmailChatVO.getRecipientEmailAddr());

            //发件人姓名
            entity.setSenderFirstName(inquireAllEmailChatVO.getFirstName());
            entity.setSenderLastName(inquireAllEmailChatVO.getLastName());
            entity.setMessage(InquiryReplyUtil.replyRepMessage(inquireAllEmailChatVO.getMessage()));
            entity.setCreateDate(inquireAllEmailChatVO.getCreateDate());

            // refactor add
            List<InquiryAttachmentVO> attachmentList = inquiryAllDao.getInquiryAttachmentArrayByReplyId(inquireAllEmailChatVO.getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(attachmentList)) {
                attachmentList = inquiryAllDao.getInquiryFileArrayByReplyId(inquireAllEmailChatVO.getReplyId());
            }

            if (CollectionUtils.isNotEmpty(attachmentList)) {
                entity.setAttachment(amazonUtil.appRfiFileList(attachmentList, i));
            }
            entity.setInquiryType(InquiryTypeEnum.getEnumByKey(inquiryDetailVO.getInquiryType().getKey()));

            //抄送邮件
            List<RfiInquiryEmailAddressCoreVO> rfiInquiryEmailAddressCoreVOS = inquiryAllEmailDao.selectInquireEmailList(chatEntityList.get(i).getReplyId());
            if (CollectionUtils.isNotEmpty(rfiInquiryEmailAddressCoreVOS)) {
                entity.setCc(OrikaMapperUtil.coverList(rfiInquiryEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
            }

            inquiryDetailVOList.add(entity);
        }
        inquiryDetailVOList = inquiryDetailVOList.stream().sorted(Comparator.comparing(APPInquiryDetailVO::getCreateDate).reversed()).collect(Collectors.toList());
        appInquiryReplyChatVO.setInquiryDetailList(inquiryDetailVOList);


        //消息已读状态
        InquirySupplierStatusDTO entity = new InquirySupplierStatusDTO();
        if (Boolean.TRUE.equals(adminFlag)) {
            entity.setMasterAcctReadFlag(true);
            //如果这个是主账号负责的，同时更新已读状态
            if(userVO.getUserId().equals(supplierInquireStateEntity.getSupplierUserId())){
                entity.setReadFlag(true);
                /**
                 * s30需求
                 */
                inquireAllEmailChatService.markAsRead(inquiryId,1);
            }
        } else {
            entity.setReadFlag(true);
            inquireAllEmailChatService.markAsRead(inquiryId,1);
        }
        supplierInquiryService.updateInquiryState(entity, inquiryId);

        //查询卖家公司信息
        InquiryCompanyVO inquiryCompanyVO = new InquiryCompanyVO();
        UserBaseProfileVO userBaseProfileVO = userService.getUserBaseProfile(supplierInquireStateEntity.getSupplierUserId());
        SupplierCommonInfoDTO supplierInfo = supplierService.getSupplierCommonInfo(supplierInquireStateEntity.getSupplierId());
        if (ObjectUtils.isNotEmpty(userBaseProfileVO)) {
            inquiryCompanyVO.setCompanyName(userBaseProfileVO.getCompanyInfo().getCompanyName());
            inquiryCompanyVO.setEmailAddress(userBaseProfileVO.getContactInfo().getEmail());
            inquiryCompanyVO.setFirstName(userBaseProfileVO.getContactInfo().getFirstName());
            inquiryCompanyVO.setLastName(userBaseProfileVO.getContactInfo().getLastName());
            inquiryCompanyVO.setRoleName(userBaseProfileVO.getContactInfo().getJobTitle());
            StringBuilder phone = new StringBuilder();
            phone.append("(" + userBaseProfileVO.getContactInfo().getTelCountryCode() + ")");
            phone.append(userBaseProfileVO.getContactInfo().getTelAreaCode());
            phone.append(userBaseProfileVO.getContactInfo().getPhone());
            inquiryCompanyVO.setPhone(phone.toString());
            inquiryCompanyVO.setCountry(dictCountryUtils.convertMapForOrderStatus(userBaseProfileVO.getContactInfo().getCountryCode()));
            if(Objects.nonNull(supplierInfo)){
                inquiryCompanyVO.setLogo(supplierInfo.getLogoUrl());
                inquiryCompanyVO.setSupplierId(String.valueOf(supplierInfo.getSupplierId()));
            }
            appInquiryReplyChatVO.setCompanyModel(inquiryCompanyVO);
        }
        appInquiryReplyChatVO.setPotentialOpportunityConvertFlag(inquireAllVO.getInquireAllItemList().getPotentialOpportunityConvertFlag());
        appInquiryReplyChatVO.setPotentialOpportunityFlag(inquireAllVO.getInquireAllItemList().getPotentialOpportunityFlag());
        appInquiryReplyChatVO.setRecommendBuyerFlag(getRecommendFlag(inquireAllVO.getBuyerType(),buyerId,inquireAllVO.getEmailAddr()));
        try {
            //webSocket通知
            rfiMsgService.sendRfiMsg(RfiMsgVO.builder().supplierId(supplierInquireStateEntity.getSupplierId()).userId(supplierInquireStateEntity.getSupplierUserId()).build());
        } catch (Exception exception) {
            log.error("webSocket rfi notice error, supplierId = {}, userId = {}", supplierInquireStateEntity.getSupplierId(), supplierInquireStateEntity.getSupplierUserId());
        }
        return Result.success(appInquiryReplyChatVO);
    }

    private Boolean getRecommendFlag(Integer[] buyerType, Long buyerId, String emailAddr) {
        try {
            if(Objects.isNull(buyerType) || buyerType.length==0){
                return false;
            }
            buyerType = Arrays.stream(buyerType)
                    .filter(e -> !e.equals(8) && !e.equals(9))
                    .toArray(Integer[]::new);
            return buyerType.length>0 && !isBlackList(buyerId,emailAddr);
        }catch (Exception e){
            log.error("getRecommendFlag error buyerType :{} buyerId :{} email :{} error :{}",buyerType, buyerId,emailAddr,JSON.toJSONString(e));
        }
        return false;
    }

    private boolean isBlackList(Long buyerId,String email) {
        try {
            return ResultUtil.getData(rfqBlackListFeign.isBlackListUser(buyerId,email));
        }catch (Exception e){
            log.error("isBlackList error buyerId :{} email :{} error :{}", buyerId,email,JSON.toJSONString(e));
            return false;
        }
    }

    @SneakyThrows
    @Override
    public List<ResponseAttachmentVO> fileUpload(List<InquiryAttachmentVO> files, int index) {
        List<String> fileKeys = new ArrayList<>();
        files.stream().forEach(att -> {
            if (!"old".equals(att.getAttachmentType()) && index != 0) {
                fileKeys.add(att.getUrl());
            }
            if ("new".equals(att.getAttachmentType())) {
                fileKeys.add(att.getUrl());
            }
        });

        JSONObject jsonObject = attachmentService.batchGetFileUrl(fileKeys, "rfi", 1);
        if (Objects.isNull(jsonObject) || jsonObject.isEmpty()) {
            return Collections.emptyList();
        }
        files.forEach(att -> {
            att.setUrl(jsonObject.get(att.getUrl()).toString());
            att.setType(FileUtil.fileMimeType(att.getName()));
            att.setSourceType(SourceNameEnum.MESSAGE.getKey());
        });
        return OrikaMapperUtil.coverList(files, ResponseAttachmentVO.class);
    }

    @Override
    public List<InquiryChatMessageAggDTO> getHistoryMessageOfInquiry(String inquiryId, Long buyerId, InquiryCoreConstants.OrderType orderType, Boolean containMainMessage) {
        List<InquiryChatMessageAggDTO> historyMessage = Lists.newArrayList();
        InquirySupplierStatusVO supplierInquireStateEntity = inquiryStateInfo(inquiryId);
        UserVO buyerUserVO = userService.getUserInfo(buyerId);

        if (ObjectUtils.isEmpty(buyerUserVO)) {
            log.info("=================================================");
            log.info("history message invoker user empty user id ={}, inquiryId = {} ", buyerId, inquiryId);
            log.info("=================================================");
            return new ArrayList<>();
        }
        String buyerFirstName = buyerUserVO.getFirstName();
        String buyerLastName = buyerUserVO.getLastName();
        //查询所有的回复记录
        List<InquireAllEmailChatVO> chatEntityList = inquiryAllEmailChatDao.inquiryAllEmailChatList(inquiryId,false);
        Long inquirySupplierUserId = null;
        if (Objects.nonNull(supplierInquireStateEntity)) {
            inquirySupplierUserId = supplierInquireStateEntity.getSupplierUserId();
        }
        if (CollectionUtils.isNotEmpty(chatEntityList)) {
            for (InquireAllEmailChatVO chat : chatEntityList) {
                if (Objects.nonNull(chat)) {
                    InquiryChatMessageAggDTO chatMessageDto = new InquiryChatMessageAggDTO();
                    //type: 1:buyer回复, 2:supplier回复
                    if (InquiryCoreConstants.InquireChatMessageType.BUYER.getKey().equals(chat.getMessageType())) {
                        //收件人->供应商id
                        Long supplierUserId = chat.getSenderUserId();
                        if (Objects.isNull(supplierUserId) && Objects.nonNull(supplierInquireStateEntity)) {
                            //取不到供应商id, 兜底
                            //这里有问题，修改逻辑
                            supplierUserId = inquirySupplierUserId;
                        }
                        UserVO sellerUserVo = userService.getUserInfo(supplierUserId);

                        //收件人
                        chatMessageDto.setFirstName(Objects.nonNull(sellerUserVo) ? sellerUserVo.getFirstName() : StringUtils.EMPTY);
                        chatMessageDto.setLastName(Objects.nonNull(sellerUserVo) ? sellerUserVo.getLastName() : StringUtils.EMPTY);
                        //发件人的邮箱,类型为买家回复 没有发件人邮箱 inquireAllEmailChat 的 email_addr (发件人邮箱) 没有数据 从buyerUserVO取
                        chatMessageDto.setSenderEmail(buyerUserVO.getEmail());
                    } else {
                        //卖家回复 收件人
                        chatMessageDto.setFirstName(buyerFirstName);
                        chatMessageDto.setLastName(buyerLastName);
                        //发件人的邮箱(卖家) 类型为供应商回复 inquireAllEmailChat 的 email_addr才有数据
                        chatMessageDto.setSenderEmail(chat.getSenderEmailAddr());
                    }

                    //抄送邮箱
                    // refactor admin 查询 询盘详情
                    List<RfiInquiryEmailAddressCoreVO> rfiInquiryEmailAddressCoreVOS = inquiryAllEmailService.selectInquireEmailList(RfiCommonConstants.CC, chat.getReplyId());

                    // refactor remove 旧查询
                    if (CollectionUtils.isEmpty(rfiInquiryEmailAddressCoreVOS)) {
                        rfiInquiryEmailAddressCoreVOS = inquiryAllEmailAddressService.emailAddress(chat.getReplyId());
                    }

                    if (CollectionUtils.isNotEmpty(rfiInquiryEmailAddressCoreVOS)) {
                        chatMessageDto.setRfiInquiryEmailAddressList(OrikaMapperUtil.coverList(rfiInquiryEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
                    }

                    //收件人的邮箱 表里的sendEmail 指收件人Email
                    chatMessageDto.setEmailAddress(chat.getRecipientEmailAddr());

                    //发件人姓名
                    chatMessageDto.setSenderFirstName(chat.getFirstName());
                    chatMessageDto.setSenderLastName(chat.getLastName());
                    chatMessageDto.setMessage(InquiryReplyUtil.replyRepMessage(chat.getMessage()));
                    chatMessageDto.setCreateDate(chat.getCreateDate());
                    chatMessageDto.setAttachment(getAttachment(chat.getReplyId()));
                    historyMessage.add(chatMessageDto);
                }
            }
        }
        //默认是倒序
        if (InquiryCoreConstants.OrderType.ASC.equals(orderType)) {
            historyMessage = historyMessage.stream().filter(Objects::nonNull).sorted(Comparator.comparingLong(dto -> Objects.nonNull(dto.getCreateDate()) ? dto.getCreateDate().getTime() : Long.MAX_VALUE)).collect(Collectors.toList());
        }
        return historyMessage;
    }

    private List<ResponseAttachmentVO> getAttachment(String replyId) {
        try {

            // refactor add
            List<InquiryAttachmentVO> attachment = inquiryAllDao.getInquiryAttachmentArrayByReplyId(replyId);

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(attachment)) {
                attachment = inquiryAllDao.getInquiryFileArrayByReplyId(replyId);
            }

            if (CollectionUtils.isNotEmpty(attachment)) {
                String sign = CipherUtil.getSign(secretKey, "rfi");
                attachment.stream().forEach(att -> {
                    FileReqDTO fileReqDTO = new FileReqDTO();
                    fileReqDTO.setFileKey(att.getUrl());
                    fileReqDTO.setSign(sign);
                    fileReqDTO.setType(1);

                    //获取最大下标
                    if (!"old".equals(att.getAttachmentType())) {
                        String url = attachmentService.getFileUrl(fileReqDTO);
                        att.setUrl(url);
                    }
                    if ("new".equals(att.getAttachmentType())) {
                        String url = attachmentService.getFileUrl(fileReqDTO);
                        att.setUrl(url);
                    }
                    att.setType(FileUtil.fileMimeType(att.getName()));
                    att.setSourceType(SourceNameEnum.MESSAGE.getKey());
                });
                return OrikaMapperUtil.coverList(attachment, ResponseAttachmentVO.class);
            }
        } catch (Exception e) {
            log.error("admin rfi detail message attachment error :{},{}", replyId, JSON.toJSONString(e));
        }
        return new ArrayList<>();
    }

    @Override
    public RfiSupplierDetailAggVO inquirySupplierInfo(String threadId) {
        InquireAllVO inquiryDetailVO = inquiryAllDao.findOneByThreadId(threadId);
        RfiSupplierDetailAggVO rfiSupplierDetailAggVO = null;
        if (ObjectUtils.isNotEmpty(inquiryDetailVO)) {
            rfiSupplierDetailAggVO = new RfiSupplierDetailAggVO();

            InquirySupplierStatusVO inquirySupplierStatusVO = inquiryStateInfo(threadId);
            SupplierCommonInfoDTO supplierCommonInfoDTO = supplierService.getSupplierCommonInfo(inquiryDetailVO.getInquireAllItemList().getSupplierId());
            String photo = userService.getUserInfo(inquirySupplierStatusVO.getSupplierUserId()).getPhoto();
            ProductDetailVo product = null;
            if (Objects.nonNull(inquiryDetailVO.getInquireAllItemList().getProductId())) {
                product = productService.getProductDetail(inquiryDetailVO.getInquireAllItemList().getProductId());
            }
            if (Objects.nonNull(product)) {
                rfiSupplierDetailAggVO.setProductName(product.getProductInfoMultiLan().getProductName());
                rfiSupplierDetailAggVO.setProductImageUrl(product.getProductPrimaryImage());
                rfiSupplierDetailAggVO.setMinOrderNumber(product.getProduct().getMinOrderQuantity());
                rfiSupplierDetailAggVO.setMinOrderUnit(product.getProduct().getMinOrderUnit());
            }
            rfiSupplierDetailAggVO.setInquiryId(threadId);
            rfiSupplierDetailAggVO.setSupplierId(inquiryDetailVO.getInquireAllItemList().getSupplierId());
            rfiSupplierDetailAggVO.setProductId(inquiryDetailVO.getInquireAllItemList().getProductId());
            rfiSupplierDetailAggVO.setPhoto(photo);
            rfiSupplierDetailAggVO.setSupplierName(Objects.nonNull(supplierCommonInfoDTO) ? supplierCommonInfoDTO.getCompanyDisplayName() : null);
            if (InquiryTypeEnum.PRODUCT.getKey().equals(inquiryDetailVO.getInquiryType().getKey()) && Objects.nonNull(product)) {
                rfiSupplierDetailAggVO.setCategoryL1(product.getCategoryInfo().getL1CategoryVo().getCategoryId());
                rfiSupplierDetailAggVO.setCategoryL2(product.getCategoryInfo().getL2CategoryVo().getCategoryId());
                rfiSupplierDetailAggVO.setCategoryL3(product.getCategoryInfo().getL3CategoryVo().getCategoryId());
                rfiSupplierDetailAggVO.setCategoryL4(product.getCategoryInfo().getL4CategoryVo().getCategoryId());
            }
            rfiSupplierDetailAggVO.setMaxStartLevel(Objects.nonNull(supplierCommonInfoDTO) ? supplierCommonInfoDTO.getMaxContractLevel().longValue() : null);
            rfiSupplierDetailAggVO.setSupplierType(Objects.nonNull(supplierCommonInfoDTO) ? supplierCommonInfoDTO.getSupplierType() : null);
            rfiSupplierDetailAggVO.setSupplierUserId(inquirySupplierStatusVO.getSupplierUserId());
        }
        return rfiSupplierDetailAggVO;
    }

    @Override
    public Boolean inSiteNotification(Long userId, Long supplierId, String inquiryId) {
        InquiryAllItemEntity inquiryAllItemEntity = inquiryAllItemDao.selectOne(new LambdaQueryWrapper<InquiryAllItemEntity>()
                .eq(InquiryAllItemEntity::getThreadId, inquiryId));
        if (Objects.isNull(inquiryAllItemEntity)) {
            log.error("inSiteNotification error,InquireAllItemEntity is null,dto:{}", inquiryId);
            return false;
        }

        InquirySupplierStatusEntity supplierInquireStateEntity = inquirySupplierStatusDao.selectOne(new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, inquiryId));
        if (Objects.isNull(supplierInquireStateEntity)) {
            log.error("inSiteNotification error,supplierInquireStateEntity is null,dto:{}", inquiryId);
            return false;
        }
        return supplierInquireStateEntity.getSupplierId().equals(supplierId) && supplierInquireStateEntity.getSupplierUserId().equals(userId);
    }

    @Override
    public List<String> getInquiryTypeList(InquiryTypeListDTO dto) {
        List<String> result = new ArrayList<>();
        List<String> data = inquiryAllDao.getInquiryTypeList(dto);
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (String type : data) {
            if ("Y".equals(type)) {
                result.add(RfiCommonConstants.ONE_TO_MANY);
            } else {
                result.add(RfiCommonConstants.ONE_TO_ONE);
            }
        }
        return result.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public SupplierInquiryListTotalVO supplierInquiryListTotal(Long supplierId, Long supplierUserId, Boolean suppAdminFlag, Integer tabType) {
        return null;
    }

    @Override
    public SupplierInquiryTotalVO supplierInquiryTotal(Long supplierId, Long supplierUserId) {
        return null;
    }

    @Override
    public int inquiryDel(InquiryDeleteDTO dto) {
        return inquirySupplierStatusDao.deleteInquiry(dto);
    }

    @Override
    public int supplierRecentInquireTotalByOrgId(Long supplierId, Long supplierUserId, Integer recentDays) {
        return 0;
    }

    @Override
    public int markReply(Long userId, String threadId) {
        log.info("supplier chart reply flag, userId = {}, threadId = {}", userId, threadId);
        InquirySupplierStatusEntity inquirySupplierStatusEntity = InquirySupplierStatusEntity.builder().chatReplyFlag(true).distFlag(true).replyFlag(true).build();

        //更新询盘已回复
        InquiryAllItemEntity inquiryAllItemEntity = InquiryAllItemEntity.builder().replyFlag(true).build();
        inquiryAllItemDao.update(inquiryAllItemEntity, new LambdaQueryWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getThreadId, threadId));
        //更新询盘消息已回复
        return inquirySupplierStatusDao.update(inquirySupplierStatusEntity, new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, threadId));
    }

    @Override
    public InquirySupplierStatusVO selectRfiSupplierInfo(Long supplierId, Long supplierUserId, String threadId) {
        InquirySupplierStatusVO inquirySupplierStatusVO = new InquirySupplierStatusVO();
        InquirySupplierStatusEntity inquirySupplierStatusEntity = inquirySupplierStatusDao.selectOne(new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, threadId).eq(InquirySupplierStatusEntity::getSupplierId, supplierId)
                .eq(InquirySupplierStatusEntity::getSupplierUserId, supplierUserId));

        if (ObjectUtils.isNotEmpty(inquirySupplierStatusEntity)) {
            BeanUtils.copyProperties(inquirySupplierStatusEntity, inquirySupplierStatusVO);
            return inquirySupplierStatusVO;
        }
        return null;
    }

    @Override
    public InquirySupplierStatusVO inquiryStateInfo(String threadId) {
        InquirySupplierStatusEntity supplierInquireStateEntity = inquirySupplierStatusDao.selectOne(new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity:: getThreadId, threadId));
        if (ObjectUtils.isNotEmpty(supplierInquireStateEntity)) {
            InquirySupplierStatusVO supplierInquireStateVO = new InquirySupplierStatusVO();
            BeanUtils.copyProperties(supplierInquireStateEntity, supplierInquireStateVO);
            return supplierInquireStateVO;
        }
        return null;
    }

    @Override
    public int markDel(Long supplierUserId, List<String> threadList) {
        return inquirySupplierStatusDao.markDel(supplierUserId, threadList);
    }

    @Override
    public int restoreDel(Long supplierUserId, List<String> threadList) {
        return inquirySupplierStatusDao.restoreDel(supplierUserId, threadList);
    }

    @Override
    public Boolean matchedFeedback(String threadId, Boolean feedback) {
        LambdaUpdateWrapper<InquiryAllItemEntity> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(InquiryAllItemEntity::getThreadId,threadId);
        wrapper.set(InquiryAllItemEntity::getSuppFeedbackMatch,feedback);
        wrapper.set(InquiryAllItemEntity::getSuppFeedbackDate,new Date());

        return inquiryAllItemDao.update(null,wrapper)>0;
    }

    @Override
    public int inquiryRubbish(Long supplierId, Long supplierUserId, IdsVO ids) {
        InquirySupplierStatusEntity entity = InquirySupplierStatusEntity.builder()
                .rubbishFlag(true).build();
        return inquirySupplierStatusDao.update(entity, new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .in(InquirySupplierStatusEntity:: getThreadId, ids.getInquiryIds())
                .eq(InquirySupplierStatusEntity:: getSupplierUserId, supplierUserId)
                .eq(InquirySupplierStatusEntity::getSupplierId, supplierUserId));
    }

    private String getCategoryNameByProductId(String productId) {
        String productCategoryName = StringUtils.EMPTY;
        if (NumberUtil.isLong(productId)) {
            ProductTrackingVO productTrackingVo = productService.getProductTracking(Long.parseLong(productId));
            if (Objects.nonNull(productTrackingVo) && Objects.nonNull(productTrackingVo.getL4CategoryId())) {
                Long l4CategoryId = productTrackingVo.getL4CategoryId();
                ProductCategoryVO productCategoryVO = categoryService.getCategoryById(l4CategoryId);
                if (Objects.nonNull(productCategoryVO) && Objects.nonNull(productCategoryVO.getCategoryName())) {
                    productCategoryName = productCategoryVO.getCategoryName();
                }
            }
        }
        return productCategoryName;
    }

    @Override
    public Result<SupplierInquiryTotalVO> supplierNoReadInquiryStatistics(Long supplierId, Long supplierUserId, UserVO userInfo) {
        SupplierInquiryTotalVO supplierInquiryTotalVO = new SupplierInquiryTotalVO();
        if (Objects.nonNull(userInfo)) {
            BaseSupplierVO currentSupplier = userInfo.getCurrentSupplier();
            if (Objects.nonNull(currentSupplier)) {
                Boolean adminFlag = supplierService.hasSupplierRfqAndRfiViewPermissions(userInfo);
                Long querySupplierUserId = null;
                if (Boolean.FALSE.equals(adminFlag)) {
                    //非主账号只查当前用户
                    querySupplierUserId = supplierUserId;
                }
                supplierInquiryTotalVO.setInquiryNewToOne(inquirySupplierStatusDao.supplierToOneInquiryNoReadTotal(supplierId, querySupplierUserId));
                supplierInquiryTotalVO.setInquiryNewToMany(inquirySupplierStatusDao.supplierToManyInquiryNoReadTotal(supplierId, querySupplierUserId));
            }
        }
        return Result.success(supplierInquiryTotalVO);
    }

    @Override
    public Integer supplierNoReadInquiryTotal(Long supplierId, Long supplierUserId, Boolean isMaster) {
        Long querySupplierUserId = null;
        if (Boolean.FALSE.equals(isMaster)) {
            //非主账号只查当前用户
            querySupplierUserId = supplierUserId;
        }
        Integer result = inquirySupplierStatusDao.supplierInquiryNoReadTotal(supplierId, querySupplierUserId);
        return Optional.ofNullable(result).orElse(BigInteger.ZERO.intValue());
    }

    @Override
    public Result<Integer> getSupplierNoReplyInquiryTotal(Integer tabType, UserVO userInfo) {
        Boolean adminFlag = supplierService.hasSupplierRfqAndRfiViewPermissions(userInfo);
        return Result.success(inquirySupplierStatusDao.supplierNoReplyInquiryTotal(tabType,userInfo.getCurrentSupplier().getSupplierId(), Boolean.TRUE.equals(adminFlag)?null:userInfo.getUserId()));
    }

    @Override
    public Result<Integer> supInqReassign(SupplierReassignDTO dto) {
        int count = 0;
        Integer dbCount = inquirySupplierStatusDao.selectCount(new LambdaQueryWrapper<InquirySupplierStatusEntity>().eq(InquirySupplierStatusEntity::getSupplierId,dto.getSupplierId()).in(InquirySupplierStatusEntity::getThreadId, Arrays.asList(dto.getInquiryIds())));
        if(!Objects.equals(dbCount,dto.getInquiryIds().length)){
            //当前分配的inquiryId有部分不属于当前登录用户的供应商，恶意分配
            log.warn("supInqReassign error,target inquiryId wrong,dto:{}", JSON.toJSONString(dto.getInquiryIds()));
            throw new BusinessException(ResultCode.RfiResultCode.RFI_THREAD_REASSIGN);
        }
        try {
            UserVO userVO = userService.getUserInfo(dto.getSupplierUserId());
            dto.setFirstName(userVO.getFirstName());
            dto.setLastName(userVO.getLastName());
            dto.setEmail(userVO.getEmail());
            count = this.supplierReassign(dto);
            rfiMsgService.sendRfiMsg(RfiMsgVO.builder().supplierId(dto.getSupplierId()).userId(dto.getSupplierUserId()).build());
        } catch (Exception e) {
            log.error("rfi-reassign error, dto :{} error :{}", dto,JSON.toJSONString(e));
        }
        return Result.success(count);
    }

    public Integer supplierReassign(SupplierReassignDTO supplierReassignDTO) {
        try {
            log.info("rfi-reassign, SupplierReassignDTO :{}", supplierReassignDTO);
            InquirySupplierStatusEntity supplierStatusEntity = new InquirySupplierStatusEntity();
            supplierStatusEntity.setSupplierUserId(supplierReassignDTO.getSupplierUserId());
            supplierStatusEntity.setFirstName(supplierReassignDTO.getFirstName());
            supplierStatusEntity.setLastName(supplierReassignDTO.getLastName());
            supplierStatusEntity.setEmailAddr(supplierReassignDTO.getEmail());
            inquirySupplierStatusDao.update(supplierStatusEntity, new LambdaQueryWrapper<InquirySupplierStatusEntity>().in(InquirySupplierStatusEntity::getThreadId, supplierReassignDTO.getInquiryIds()));
            inquiryAllItemDao.update(InquiryAllItemEntity.builder().redistributionFlag(true).lUpdDate(new Date()).build(), new LambdaQueryWrapper<InquiryAllItemEntity>().in(InquiryAllItemEntity::getThreadId, supplierReassignDTO.getInquiryIds()));

            List<InquiryAllItemEntity> oppoList = inquiryAllItemDao.selectList(new LambdaQueryWrapper<InquiryAllItemEntity>()
                    .eq(InquiryAllItemEntity::getPotentialOpportunityFlag,Boolean.TRUE)
                    .eq(InquiryAllItemEntity::getPotentialOpportunityConvertFlag,Boolean.FALSE)
                    .in(InquiryAllItemEntity::getThreadId, supplierReassignDTO.getInquiryIds()));

            if(CollectionUtils.isNotEmpty(oppoList)){
                List<String> oppoItemThreadIdList = oppoList.stream().map(InquiryAllItemEntity::getThreadId).collect(Collectors.toList());

                log.info("rfi-reassign oppoItemIdList :{}", oppoItemThreadIdList);

                //s69 潜在商机转换标记
                inquiryAllItemService.markPotentialOpportunityConvert(oppoItemThreadIdList,supplierReassignDTO.getSource());

                commonService.syncPscCmAndScPoint(oppoList);
                commonService.sensorData(oppoItemThreadIdList);
            }

            //记录状态 @InquiryStatus(status = "reassign")
            List<String> list = Lists.newArrayList(supplierReassignDTO.getInquiryIds());
            Boolean result = inquiryStatusService.batchSaveInquiryStatus(list, supplierReassignDTO.getSupplierUserId(), supplierReassignDTO.getEmail(), InquiryStatusEnum.REASSIGN);
            return Boolean.TRUE.equals(result) ? 1 : 0;
        } catch (Exception e) {
            log.error("rfi-reassign error, supplierReassignDTO:{}", supplierReassignDTO, e);
        }
        return 0;
    }

    @Override
    public boolean markPotentialOpportunityConvert(List<String> threadIdList,String source) {
        log.info("markPotentialOpportunityConvert threadIdList :{} source :{}", threadIdList,source);
        //s69 潜在商机转换标记
        return inquiryAllItemDao.update(InquiryAllItemEntity.builder().potentialOpportunityConvertFlag(true).lUpdDate(new Date()).potentialOpportunityConvertSource(source).build(),
                new LambdaQueryWrapper<InquiryAllItemEntity>()
                        .eq(InquiryAllItemEntity::getPotentialOpportunityFlag,Boolean.TRUE)
                        .eq(InquiryAllItemEntity::getPotentialOpportunityConvertFlag,Boolean.FALSE)
                        .in(InquiryAllItemEntity::getThreadId, threadIdList))>0;
    }

    @Override
    public Result<String> sendReassignEmail(Long masterSupplierUserId, Long supplierUserId, Long supplierId, String[] idsVO) {
        for (String str : idsVO) {
            inquireAllService.sendReassignEmail(masterSupplierUserId, supplierUserId, supplierId, str);
        }
        return Result.success();
    }

    @Override
    public PageResult<SupplierPotentialOpportunityVO> potentialOpportunityList(SupplierPotentialOpportunityQueryDTO potentialOpportunityDTO) {

        if (CollectionUtils.isNotEmpty(potentialOpportunityDTO.getBuyerLocationList())) {
            potentialOpportunityDTO.setCountryCodeListFilter(countryCodeDic(potentialOpportunityDTO.getCountryCodeListFilter(), potentialOpportunityDTO.getBuyerLocationList()));
        }
        if (CollectionUtils.isNotEmpty(potentialOpportunityDTO.getRfiSourceList())) {
            buildRfiSourceList(potentialOpportunityDTO.getRfiSourceList());
        } else {
            potentialOpportunityDTO.setRfiSourceList(RfiSourceEnum.getNonAdminRfiSource());
        }
        potentialOpportunityDTO.setSortField(Optional.ofNullable(InquiryCoreConstants.SortField.match(potentialOpportunityDTO.getSortFieldKey())).orElse(InquiryCoreConstants.SortField.L_UPD_DATE.getValue()));
        potentialOpportunityDTO.setOrderType(Optional.ofNullable(InquiryCoreConstants.OrderType.match(potentialOpportunityDTO.getOrderTypeKey())).orElse(InquiryCoreConstants.OrderType.DESC.getValue()));

        potentialOpportunityDTO.setDays(deletePotentialOpportunityDays);

        Page<SupplierPotentialOpportunityVO> page = new Page<>(potentialOpportunityDTO.getPageNum(), potentialOpportunityDTO.getPageSize());
        IPage<SupplierPotentialOpportunityVO> pageResult = inquiryAllDao.potentialOpportunityList(page, potentialOpportunityDTO);


        // 传入inquireId   则判断是否存在列表  不存在多查询一次
        if (StringUtils.isNotBlank(potentialOpportunityDTO.getInquiryId())) {
            boolean isContain = pageResult.getRecords().stream().anyMatch(m -> m.getInquiryId().equals(potentialOpportunityDTO.getInquiryId()));
            if (Boolean.FALSE.equals(isContain)) {
                // 不包含inquiry 查询一次
                potentialOpportunityDTO.setInquiryFlag("true");
                SupplierPotentialOpportunityVO potentialOpportunityVO = inquiryAllDao.potentialOpportunityByThreadId(potentialOpportunityDTO);
                if (Objects.nonNull(potentialOpportunityVO)) {
                    if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                        List<SupplierPotentialOpportunityVO> recordList = new ArrayList<>();
                        pageResult.setRecords(recordList);
                    }
                    pageResult.getRecords().add(0, potentialOpportunityVO);
                }
            }
        }


        PageResult<SupplierPotentialOpportunityVO> data = new PageResult<>();
        data.setList(pageResult.getRecords());
        data.setTotal(pageResult.getTotal());
        data.setPageNum(pageResult.getCurrent());
        data.setPageSize(pageResult.getSize());
        data.setTotalPage(pageResult.getPages());
        Map<String,String> countryMap = dictService.getDicKeyValueMap(DicGroupNameEnum.COUNTRY.getValue(),"enus");
        data.getList().forEach(item -> {
            item.setCountry(countryMap.getOrDefault(item.getCountry(),""));
            item.setFirstInquiryFlag(!Objects.equals("Y", item.getSupplierType()));
            item.setRecommendBuyerFlag(Objects.nonNull(item.getBuyerType()) && item.getBuyerType().length>0);
        });
        return data;
    }

    @Override
    public SupplierPotentialOpportunityListTotalVO potentialOpportunityStateCount(SupplierPotentialOpportunityStatusCountDTO potentialOpportunityStateCountDTO) {

        if (CollectionUtils.isNotEmpty(potentialOpportunityStateCountDTO.getBuyerLocationList())) {
            potentialOpportunityStateCountDTO.setCountryCodeListFilter(countryCodeDic(potentialOpportunityStateCountDTO.getCountryCodeListFilter(), potentialOpportunityStateCountDTO.getBuyerLocationList()));
        }
        if (CollectionUtils.isNotEmpty(potentialOpportunityStateCountDTO.getRfiSourceList())) {
            buildRfiSourceList(potentialOpportunityStateCountDTO.getRfiSourceList());
        } else {
            potentialOpportunityStateCountDTO.setRfiSourceList(RfiSourceEnum.getNonAdminRfiSource());
        }

        potentialOpportunityStateCountDTO.setDays(deletePotentialOpportunityDays);

        SupplierPotentialOpportunityListTotalVO potentialOpportunityListTotalVO = inquirySupplierStatusDao.potentialOpportunityStateCount(potentialOpportunityStateCountDTO);
        return Optional.ofNullable(potentialOpportunityListTotalVO).orElse(new SupplierPotentialOpportunityListTotalVO());
    }

    @Override
    public Result<Boolean> inquiryPin(String threadId, long userId, boolean pinFlag) {
        return Result.success(inquirySupplierStatusDao.update(null, new LambdaUpdateWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, threadId)
                .eq(InquirySupplierStatusEntity::getSupplierUserId, userId)
                .set(InquirySupplierStatusEntity::getPinTopDate,pinFlag?new Date():null))>0);
    }

    @Override
    public Result<Boolean> masterInquiryPin(String threadId, long userId,boolean pinFlag) {
        return Result.success(inquirySupplierStatusDao.update(null, new LambdaUpdateWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, threadId)
                .eq(InquirySupplierStatusEntity::getSupplierUserId, userId)
                .set(InquirySupplierStatusEntity::getMasterPinTopDate,pinFlag?new Date():null))>0);
    }

}
