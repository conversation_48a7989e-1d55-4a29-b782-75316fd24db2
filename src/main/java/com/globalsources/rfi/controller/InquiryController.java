/**
 * <a>Title: Inquiry<PERSON>ontroller </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：创建询盘<a>
 *
 * <AUTHOR>
 * @date 2021/7/12-19:56
 */
package com.globalsources.rfi.controller;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.globalsources.awesome.logging.annotation.AwesomeLog;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.BeanUtil;
import com.globalsources.rfi.agg.core.dto.InquireAllItemDTO;
import com.globalsources.rfi.agg.core.dto.mc.BuyerProfile;
import com.globalsources.rfi.agg.core.dto.mc.RequestRfiBuildDTO;
import com.globalsources.rfi.agg.core.dto.mc.SupplierProfile;
import com.globalsources.rfi.agg.core.dto.rfi.InquireAllEmailAddressCoreDTO;
import com.globalsources.rfi.agg.core.vo.BuyerEmailTemplateVO;
import com.globalsources.rfi.agg.core.vo.InquireAllItemVO;
import com.globalsources.rfi.agg.core.vo.SearchInquiryVO;
import com.globalsources.rfi.agg.core.vo.record.RfiDoiResultVO;
import com.globalsources.rfi.agg.core.vo.record.RfiTMXResultVO;
import com.globalsources.rfi.agg.dto.inquiry.*;
import com.globalsources.rfi.agg.enums.*;
import com.globalsources.rfi.agg.request.EmailTemplateDTO;
import com.globalsources.rfi.agg.request.ReceiveInquirySupplierDTO;
import com.globalsources.rfi.agg.request.RequestInquiryDTO;
import com.globalsources.rfi.agg.request.SupplierInquiryDTO;
import com.globalsources.rfi.agg.request.SupplierPotentialOpportunitySummaryDTO;
import com.globalsources.rfi.agg.response.InquiryChatInfoVO;
import com.globalsources.rfi.agg.request.*;
import com.globalsources.rfi.agg.response.ReqInquiryVO;
import com.globalsources.rfi.agg.response.SupplierPotentialOpportunitySummaryVO;
import com.globalsources.rfi.agg.response.rfi.*;
import com.globalsources.rfi.data.dao.*;
import com.globalsources.rfi.data.entity.*;
import com.globalsources.rfi.service.*;
import com.globalsources.rfx.util.IdGenerator;
import com.google.common.collect.Lists;
import com.globalsources.awesome.logging.annotation.Logging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/rfi")
@Api(tags = "买家询盘列表")
@RefreshScope
public class InquiryController {

    @Autowired
    private InquiryAllService inquireAllService;

    @Resource
    private InquiryAllDao inquiryAllDao;

    @Autowired
    private InquiryStatusService inquiryStatusService;

    @Value(value = "${rfi.doi.min:7200}")
    private Integer doiMin;

    @Value(value = "${rfi.upsell.delay.hour}")
    private Integer upsellDelayHour;

    @Value(value = "${rfi.upsell.max.days:-2}")
    private Integer upsellMaxDays;

    @Value(value = "${rfi.tmx.days:7}")
    private Integer tmxDays;

    @Autowired
    private InquiryBuyerStatusDao inquiryBuyerStatusDao;

    @Autowired
    private InquirySupplierStatusDao inquirySupplierStatusDao;

    @Autowired
    private InquiryAllEmailChatDao inquiryAllEmailChatDao;

    @Autowired
    private InquiryAllService inquiryAllService;

    @Autowired
    private InquiryAllFileAttachmentDao inquiryAllFileAttachmentDao;

    @Autowired
    private InquiryAllFileAttachmentService inquireAllFileAttachmentService;

    @Autowired
    private InquiryAllEmailAddressService inquiryAllEmailAddressService;

    @Autowired
    private InquiryAllEmailDao inquiryAllEmailDao;

    @Autowired
    private BuyerEmailTemplateDao buyerEmailTemplateDao;

    @Autowired
    private InquiryAllItemService inquiryAllItemService;

    @Autowired
    private InquiryAllItemDao inquiryAllItemDao;

    @Value(value = "${rfi.upsell.unreply.day.limit:14}")
    private Integer upsellUnReplyDayLimit;

    @Value(value = "${rfi.convert.rfq.unreply.max:14400}")
    private Integer upsellUnReplyMaxMinute;

    @Value(value = "${rfi.delete.potential.opportunity.dyas:60}")
    private int deletePotentialOpportunityDays;

    @PostMapping(value = "/message-temp")
    @ApiOperation(value = "消息模板")
    @Logging(businessId = "#emailTemplateDTO.productId", businessType = "AGG-B10017", errorTag = "RFI-AGG-B10017")
    public Result<String> messageTemp(@RequestBody EmailTemplateDTO emailTemplateDTO, @RequestHeader(value = "lang",required = false,defaultValue = "enus")String lang) {
        return inquireAllService.messageTemp(emailTemplateDTO,lang);
    }

    @ApiOperation(value = "获取快捷消息列表")
    @GetMapping(value = "/quick-msg-list")
    public Result<List<String>> quickMessageList(@RequestParam("messageType") String messageType, @RequestParam(value = "lang", required = false, defaultValue = "enus") String lang) {
        return inquireAllService.quickMessageList(messageType, lang);
    }

    @ApiOperation(value = "获取快捷消息列表V2,带排序和模板id")
    @PostMapping(value = "/v2/quick-msg-list")
    Result<List<InquiryTemplateVO>> quickMessageListV2(@RequestBody InquiryTemplateListDTO build){
        return inquireAllService.quickMessageListV2(build);
    }

    @ApiOperation(value = "快捷消息使用上报")
    @PostMapping(value = "/v1/quick-msg-statistics")
    Result<Boolean> quickMessageStatistics(@RequestBody InquiryTemplateStatisticsDTO dto){
        return inquireAllService.quickMessageStatistics(dto);
    }

    @AwesomeLog
    @PostMapping(value = "/inquire-now")
    @ApiOperation(value = "创建询盘")
    @Logging(businessType = "AGG-B10018", errorTag = "RFI-AGG-B10018")
    public Result<List<String>> inquireNow(@RequestBody RequestInquiryDTO requestInquiryDTO,
                                           @RequestHeader(value = "lang",required = false,defaultValue = "enus")String lang,
                                           @RequestHeader(value = "version",required = false)String appVersion) {
        requestInquiryDTO.setLangCode(lang);
        requestInquiryDTO.setAppVersion(appVersion);
        return inquireAllService.inquiryNow(requestInquiryDTO);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/inquiry-mark-read/{userId}")
    @ApiOperation(value = "标记已读未读")
    @Logging(businessId = "#userId", businessType = "AGG-B10019", errorTag = "RFI-AGG-B10019")
    public Result<String> inquiryMarkRead(@PathVariable Long userId, @RequestBody ReqInquiryVO reqInquiryVO, @RequestParam RoleTypeEnum roleType) {
        return inquireAllService.inquiryMarkRead(userId, reqInquiryVO, roleType);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/inquiry-mark-starred/{userId}")
    @ApiOperation(value = "增加星标")
    @Logging(businessId = "#userId", businessType = "AGG-B10020", errorTag = "RFI-AGG-B10020")
    public Result<Boolean> inquiryMarkStarred(@PathVariable Long userId, @RequestBody ReqInquiryVO reqInquiryVO, @RequestParam RoleTypeEnum roleType) {
        return inquireAllService.inquiryMarkStarred(userId, reqInquiryVO, roleType);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @GetMapping(value = "/rfi-detail/{inquiryId}")
    @ApiOperation(value = "rfi详情")
    @Logging(businessId = "#inquiryId", businessType = "AGG-B10021", errorTag = "RFI-AGG-B10021")
    public Result<SearchInquiryVO> rfiDetail(@PathVariable String inquiryId) {
        return inquireAllService.rfiDetail(inquiryId);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "询盘详情chat供应商信息", notes = "买家详情chat供应商信息", tags = {"Rfi Web买家侧接口"})
    @GetMapping("/inquiry-supplier-detail/{threadId}")
    @Logging(businessId = "#threadId", businessType = "AGG-B10022", errorTag = "RFI-AGG-B10022")
    public Result<RfiSupplierDetailAggVO> inquirySupplierDetail(@PathVariable String threadId) {
        return Result.success(inquireAllService.inquirySupplierDetail(threadId));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "收到询盘的供应商数量", notes = "收到询盘的供应商数量", tags = {"Rfi Web买家侧接口"})
    @PostMapping("/receive-inquiry-supplier-count")
    public Result<Integer> receiveInquirySupplierCount(@RequestBody ReceiveInquirySupplierDTO receiveInquirySupplierDTO) {
        return Result.success(inquireAllService.receiveInquirySupplierCount(receiveInquirySupplierDTO));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "收到询盘的供应商id列表", notes = "收到询盘的供应商id列表", tags = {"Rfi Web买家侧接口"})
    @PostMapping("/receive-inquiry-supplier-list")
    public Result<List<Long>> receiveInquirySupplierList(@RequestBody ReceiveInquirySupplierDTO receiveInquirySupplierDTO) {
        return Result.success(inquireAllService.receiveInquirySupplierList(receiveInquirySupplierDTO));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "供应商收到的询盘数据", notes = "供应商收到的询盘数据", tags = {"Rfi Web买家侧接口"})
    @PostMapping("/inquiry-by-supplier")
    public Result<List<SupplierInquiryDTO>> inquiryDataBySupplierAndDate(@RequestBody ReceiveInquirySupplierDTO receiveInquirySupplierDTO) {
        return Result.success(inquireAllService.inquiryDataBySupplierAndDate(receiveInquirySupplierDTO));
    }


    @GetMapping("/block-inquiry-for-ba")
    public Result<List<NewGsolBlockInquiryVO>> getNewGBlockInquiryForBA() {
        return Result.success(inquiryAllDao.getNewGBlockInquiryForBA());
    }

    @GetMapping("/block-inquiry-report")
    public Result<List<NewGsolBlockInquiryVO>> getEBlockInquiryReport() {
        return Result.success(inquireAllService.getEBlockInquiryReport());
    }

    /**
     * 2022/11/02 job调用，从core移到agg
     * @param statusDTO
     * @return
     */
    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "修改审核状态,type=tmx/eblock,status=pending/review/reject/pass")
    @PostMapping(value = "/update-review-status")
    public Result<String> updateReviewStatus(@RequestBody InquiryStatusDTO statusDTO) {
        return inquireAllService.updateReviewStatus(statusDTO);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "查询InquiryAll")
    @PostMapping(value = "/inquiry-all/query")
    public Result<List<InquireAllVO>> queryInquiryAll(@RequestBody InquiryAllQueryDTO inquiryAllQueryDTO) {
        return inquireAllService.queryInquiryAll(inquiryAllQueryDTO);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "rfi 监控大屏数据")
    @PostMapping(value = "/monitor-inquiry/query")
    public Result<List<MonitorInquiryVO>> queryMonitorInquiry(@RequestBody MonitorInquiryQueryDTO monitorInquiryQueryDTO) {
        return inquireAllService.queryMonitorInquiry(monitorInquiryQueryDTO);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "询盘详情信息,core迁过来的,广泛用于各个地方,坑,不要轻易用")
    @GetMapping(value = "/inquiry-detail/{inquiryId}")
    @Logging(businessId = "#inquiryId",businessType = "CORE-10041", errorTag = "RFI-CORE-10041")
    @Deprecated
    public Result<com.globalsources.rfi.agg.core.vo.InquireAllVO> inquiryDetail(@PathVariable String inquiryId) {

        return Result.success(inquiryAllDao.findOne(inquiryId));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "询盘详情信息,core迁过来的,广泛用于各个地方,注意这里是threadId")
    @GetMapping(value = "/inquiry-all-info/{threadId}")
    @Logging(businessId = "#threadId",businessType = "CORE-10042", errorTag = "RFI-CORE-10042")
    public Result<com.globalsources.rfi.agg.core.vo.InquireAllVO> inquiryAllInfo(@PathVariable String threadId) {
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.setMasterRouteOnly();
            return Result.success(inquiryAllDao.findOneByThreadId(threadId));
        }
        catch (Exception e){
            log.error("inquiryAllInfo error,threadId={},error:{}",threadId, JSON.toJSONString(e));
        }
        return Result.failed();
    }

    @ApiOperation(value = "待eblock审核的rfi列表")
    @GetMapping(value = "/eblock-review-list")
    @Logging(businessType = "CORE-10055", errorTag = "RFI-CORE-10055")
    public Result<List<String>> getEblockReviewRfiList(@RequestParam Integer day) {
        List<String> resultList= Collections.emptyList();
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DATE, -day);

        List<InquiryAllEntity> rfiList= inquiryAllDao.selectList(new LambdaQueryWrapper<InquiryAllEntity>()
                .in(InquiryAllEntity::getEblockStatus, Arrays.asList(ReviewResultEnum.PENDING.getValue(),ReviewResultEnum.BLOCK.getValue(),ReviewResultEnum.REJECT.getValue()))
                .gt(InquiryAllEntity::getCreateDate,cal.getTime())
                .and(wrapper->wrapper.eq(InquiryAllEntity::getTmxStatus,ReviewResultEnum.REVIEW.getValue()).or().eq(InquiryAllEntity::getTmxStatus, ReviewResultEnum.REJECT.getValue())));
        if(CollectionUtils.isNotEmpty(rfiList)){
            resultList=rfiList.stream().map(InquiryAllEntity::getInquiryId).collect(Collectors.toList());
        }
        return Result.success(resultList);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "修改审核状态,type=tmx/eblock,status=pending/review/reject/pass")
    @GetMapping(value = "/update-review-status")
    @Logging(businessType = "CORE-10056", errorTag = "RFI-CORE-10056")
    public Result<String> updateReviewStatus(@RequestParam String inquiryId,@RequestParam String type,@RequestParam String status) {
        LambdaUpdateWrapper<InquiryAllEntity> param = new LambdaUpdateWrapper<>();
        param.eq(InquiryAllEntity::getInquiryId, inquiryId);
        if (StringUtils.isNotEmpty(type) && type.equalsIgnoreCase(ReviewTypeEnum.TMX.getKey())) {
            param.set(InquiryAllEntity::getTmxStatus, status);
        } else if (StringUtils.isNotEmpty(type) && type.equalsIgnoreCase(ReviewTypeEnum.EBLOCK.getKey())) {
            param.set(InquiryAllEntity::getEblockStatus, status);
        }
        return inquiryAllDao.update(null, param) > 1 ? Result.success() : Result.failed();
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "修改Rfi TMX 状态")
    @PostMapping(value = "/update-tmx-review-status")
    @Logging(businessId = "#inquiryId",businessType = "CORE-10057", errorTag = "RFI-CORE-10057")
    public Result<String> updateTMXReviewStatus(@RequestParam String inquiryId){
        if (inquiryAllDao.updateTMXReviewStatus(inquiryId) == 1) {
            return Result.success();
        }
        return Result.failed();
    }

    @ApiOperation(value = "待upsell的rfi列表")
    @GetMapping(value = "/pending-upsell-list")
    @Logging(businessType = "CORE-10058", errorTag = "RFI-CORE-10058")
    public Result<List<String>> getPendingUpsellRfiList(){
        List<String> resultList=Collections.emptyList();
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());

        cal.add(Calendar.HOUR, -upsellDelayHour);

        List<InquiryAllEntity> rfiList= inquiryAllDao.selectList(new LambdaQueryWrapper<InquiryAllEntity>().eq(InquiryAllEntity::getUpsellMatchFlag,false)
                .eq(InquiryAllEntity::getUpsellFlag,true)
                .and(wrapper->wrapper.between(InquiryAllEntity::getCreateDate,new DateTime().plusDays(upsellMaxDays).toDate(),cal.getTime()).or().eq(InquiryAllEntity::getUpsellEdmEmailSubmitFlag,true))
                .eq(InquiryAllEntity::getFormCompleteStatus, "Y")
                .and(wrapper->wrapper.eq(InquiryAllEntity::getTmxStatus,ReviewResultEnum.PASS.getValue()).or().eq(InquiryAllEntity::getEblockStatus,ReviewResultEnum.RELEASE.getValue()))
                .apply( " ((rfi_source = '1' and (doi_status = true or doi_status is null)) or (rfi_source != '1'))" )
        );

        if(CollectionUtils.isNotEmpty(rfiList)){
            resultList=rfiList.stream().map(InquiryAllEntity::getInquiryId).collect(Collectors.toList());
        }
        return Result.success(resultList);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "修改Rfi upsell 匹配状态")
    @GetMapping(value = "/update-upsell-match-flag")
    @Logging(businessId = "#inquiryId",businessType = "CORE-10059", errorTag = "RFI-CORE-10059")
    public Result<Boolean> updateUpsellMatchFlag(@RequestParam String inquiryId){
        LambdaUpdateWrapper<InquiryAllEntity> param = new LambdaUpdateWrapper<>();
        param.eq(InquiryAllEntity::getInquiryId, inquiryId).set(InquiryAllEntity::getUpsellMatchFlag, true);

        return Result.success(inquiryAllDao.update(null, param) > 0);
    }

    @ApiOperation(value = "获取rfi doi的状态")
    @GetMapping(value = "/get-rfi-doi-list")
    @Logging(businessType = "CORE-10061", errorTag = "RFI-CORE-10061")
    public Result<List<RfiDoiResultVO>> getRfiDoiList() {
        List<RfiDoiResultVO> doiList = inquiryAllDao.getRfiDoiList(doiMin);
        return Result.success(doiList);
    }

    @ApiOperation(value = "获取审核状态的rfi")
    @GetMapping(value = "/rfi-status-list/{status}")
    @Logging(businessType = "CORE-10054", errorTag = "RFI-CORE-10054")
    public Result<List<RfiTMXResultVO>> rfiStatusList(@PathVariable String status) {
        return Result.success(inquiryAllDao.getInquiryTMXList(status, tmxDays));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "保存RFI-MC分配关系")
    @PostMapping(value = "/build-rfi-status")
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public Result<String> buildRfiStatus(@RequestBody RequestRfiBuildDTO requestRfiBuildDTO) {
        com.globalsources.rfi.agg.core.vo.InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(requestRfiBuildDTO.getThreadId());
        if (ObjectUtils.isEmpty(inquireAllVO)){
            log.warn("inquiry buildRfiStatus warn inquireAllVO is null, threadId = {}", requestRfiBuildDTO.getThreadId());
            return Result.failed("fail");
        }
        //保存buyer询盘关系
        InquiryBuyerStatusEntity inquiryBuyerStatusEntity = new InquiryBuyerStatusEntity();
        inquiryBuyerStatusEntity.setThreadId(requestRfiBuildDTO.getThreadId());
        BuyerProfile buyerProfile = requestRfiBuildDTO.getBuyerProfile();
        inquiryBuyerStatusEntity.setBuyerId(buyerProfile.getUserId());
        inquiryBuyerStatusEntity.setEmailAddr(buyerProfile.getEmail());
        inquiryBuyerStatusEntity.setFirstName(buyerProfile.getFirstName());
        inquiryBuyerStatusEntity.setLastName(buyerProfile.getLastName());
        inquiryBuyerStatusDao.insert(inquiryBuyerStatusEntity);
        log.info("inquiry buildRfiStatus sava buyer status succ,entity:{}",inquiryBuyerStatusEntity);

        //保存supplier询盘关系
        InquirySupplierStatusEntity inquirySupplierStatusEntity = new InquirySupplierStatusEntity();
        inquirySupplierStatusEntity.setThreadId(requestRfiBuildDTO.getThreadId());
        SupplierProfile supplierProfile = requestRfiBuildDTO.getSupplierProfile();
        inquirySupplierStatusEntity.setSupplierId(supplierProfile.getSupplierId());
        inquirySupplierStatusEntity.setEmailAddr(supplierProfile.getEmail());
        inquirySupplierStatusEntity.setSupplierUserId(supplierProfile.getUserId());
        inquirySupplierStatusEntity.setFirstName(supplierProfile.getFirstName());
        inquirySupplierStatusEntity.setLastName(supplierProfile.getLastName());
        inquirySupplierStatusDao.insert(inquirySupplierStatusEntity);
        log.info("inquiry buildRfiStatus sava supplier status succ,entity:{}",inquirySupplierStatusEntity);

        //修改inquire all 状态
        InquiryAllEntity inquiryAllEntity = new InquiryAllEntity();
        inquiryAllEntity.setFormCompleteStatus("Y");
        inquiryAllEntity.setFormCompleteDate(new Date());
        inquiryAllService.update(inquiryAllEntity, new LambdaQueryWrapper<InquiryAllEntity>()
                .eq(InquiryAllEntity::getInquiryId, requestRfiBuildDTO.getInquiryId()).ne(InquiryAllEntity::getFormCompleteStatus, "Y"));

        //修改inquire all item 状态
        InquiryAllItemEntity inquiryAllItemEntity = new InquiryAllItemEntity();
        inquiryAllItemEntity.setItemCompleteFlag(Boolean.TRUE);
        inquiryAllItemService.update(inquiryAllItemEntity, new LambdaQueryWrapper<InquiryAllItemEntity>()
                .eq(InquiryAllItemEntity::getThreadId, requestRfiBuildDTO.getThreadId()));

        log.info("inquiry buildRfiStatus update inquiry all succ");

        //保存第一条buyer 发送消息
        String replyId = IdGenerator.getStringId();
        InquiryAllEmailChatEntity inquiryAllEmailChatEntity = new InquiryAllEmailChatEntity();
        inquiryAllEmailChatEntity.setInquiryId(requestRfiBuildDTO.getThreadId());
        //发件人
        inquiryAllEmailChatEntity.setSenderEmailAddr(inquiryBuyerStatusEntity.getEmailAddr());
        inquiryAllEmailChatEntity.setSenderUserId(inquiryBuyerStatusEntity.getBuyerId());
        inquiryAllEmailChatEntity.setFirstName(inquiryBuyerStatusEntity.getFirstName());
        inquiryAllEmailChatEntity.setLastName(inquiryBuyerStatusEntity.getLastName());
        //收件人
        inquiryAllEmailChatEntity.setRecipientEmailAddr(inquirySupplierStatusEntity.getEmailAddr());
        inquiryAllEmailChatEntity.setUserId(inquirySupplierStatusEntity.getSupplierUserId());

        inquiryAllEmailChatEntity.setCreateDate(new Date());
        inquiryAllEmailChatEntity.setLUpdDate(new Date());
        inquiryAllEmailChatEntity.setMessage(requestRfiBuildDTO.getThreadMessage().getBuyerMessage());
        inquiryAllEmailChatEntity.setMessageType(1);
        inquiryAllEmailChatEntity.setReplyId(replyId);
        inquiryAllEmailChatEntity.setSubject(inquireAllVO.getSubject());
        inquiryAllEmailChatDao.insert(inquiryAllEmailChatEntity);
        log.info("inquiry buildRfiStatus sava inquiry all email chat succ,entity:{}",inquiryAllEmailChatEntity);

        if (ObjectUtils.isNotEmpty(requestRfiBuildDTO.getCc())) {
            log.info("inquiry buildRfiStatus addEmailAddress,cc:{}",requestRfiBuildDTO.getCc());
            addEmailAddress(requestRfiBuildDTO.getCc(), replyId, requestRfiBuildDTO.getThreadMessage().getSubject());
        }
        //upsell
        if (InquiryTypeEnum.PRODUCT_UPSELL.getKey().equals(requestRfiBuildDTO.getInquiryType()) ||
                InquiryTypeEnum.CATEGORY.getKey().equals(requestRfiBuildDTO.getInquiryType())) {
            //新增upsell附件
            String masterThreadId = inquiryAllDao.queryMasterThreadId(inquireAllVO.getInquiryId());
            if (StringUtils.isNotEmpty(masterThreadId)) {
                //获取到第一条消息msgId
                String msgId = inquiryAllDao.queryMessageMsgId(masterThreadId);
                //查询msgId的附件关系
                List<InquiryAllFileAttachmentEntity> attachmentEntityList = inquiryAllFileAttachmentDao.selectList(new LambdaQueryWrapper<InquiryAllFileAttachmentEntity>()
                        .eq(InquiryAllFileAttachmentEntity::getInquiryId, msgId));

                if (CollectionUtils.isNotEmpty(attachmentEntityList)) {
                    attachmentEntityList.forEach(v -> v.setInquiryId(replyId));
                    inquireAllFileAttachmentService.saveBatch(attachmentEntityList);
                    log.info("inquiry buildRfiStatus attachment save batch succ,attachmentEntityList:{}",attachmentEntityList);
                }
            }
        } else {
            //修改附件
            inquiryAllFileAttachmentDao.updateInquiryIdByMessage(requestRfiBuildDTO.getThreadId(), replyId);
            log.info("inquiry buildRfiStatus attachment update succ,threadId:{},replyId:{}",requestRfiBuildDTO.getThreadId(),replyId);
        }
        log.info("inquiry buildRfiStatus all done");

        return Result.success();
    }

    @AwesomeLog(enableMethodResultLog = false)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void addEmailAddress(InquireAllEmailAddressCoreDTO emailAddressCoreDTO, String replyId, String subject) {
        InquiryAllEmailAddressEntity entity = inquiryAllEmailAddressService.getOne(new LambdaQueryWrapper<InquiryAllEmailAddressEntity>()
                .eq(InquiryAllEmailAddressEntity::getEmailAddr, emailAddressCoreDTO.getEmailAddr()));
        // refactor remove
        if (Objects.isNull(entity)) {
            entity = new InquiryAllEmailAddressEntity();
            entity.setEmailAddr(emailAddressCoreDTO.getEmailAddr());
            entity.setCreateDate(new Date());
            entity.setLUpdDate(new Date());
            entity.setEmailId(emailAddressCoreDTO.getEmailId());
            entity.setHeaderType(emailAddressCoreDTO.getHeaderType());
            entity.setName(emailAddressCoreDTO.getName());
            Snowflake snowflake = IdUtil.createSnowflake(RandomUtils.nextLong(0, 32), RandomUtils.nextLong(0, 32));
            entity.setEmailId(snowflake.nextId());
            inquiryAllEmailAddressService.save(entity);
        }
        InquiryAllEmailEntity inquiryAllEmailEntity = new InquiryAllEmailEntity();
        BeanUtils.copyProperties(emailAddressCoreDTO, inquiryAllEmailEntity);
        inquiryAllEmailEntity.setReplyId(replyId);
        // refactor remove
        inquiryAllEmailEntity.setEmailId(entity.getEmailId());
        inquiryAllEmailEntity.setSubject(subject);
        inquiryAllEmailEntity.setSentFlag("1");
        // refactor 保存 CC
        inquiryAllEmailEntity.setHeaderType(emailAddressCoreDTO.getHeaderType());
        inquiryAllEmailEntity.setUserId(emailAddressCoreDTO.getUserId());
        inquiryAllEmailEntity.setName(emailAddressCoreDTO.getName());
        inquiryAllEmailEntity.setEmailAddr(emailAddressCoreDTO.getEmailAddr());
        inquiryAllEmailDao.insert(inquiryAllEmailEntity);
    }

    @ApiOperation(value = "获取消息模板")
    @GetMapping(value = "/message-temp/{type}")
    @Logging(businessType = "CORE-10039", errorTag = "RFI-CORE-10039")
    public Result<List<BuyerEmailTemplateVO>> messageTemp(@PathVariable Integer type, @RequestHeader(value = "lang",required = false,defaultValue = "enus")String lang) {
        List<BuyerEmailTemplateEntity> result = buyerEmailTemplateDao.selectList(new LambdaQueryWrapper<BuyerEmailTemplateEntity>()
                .eq(BuyerEmailTemplateEntity::getTemplateType, type)
                .eq(BuyerEmailTemplateEntity::getLangCode,lang));
        //兼容新语言上线 && 数据库还没有对应语言模板的情况
        if(CollectionUtils.isEmpty(result) && !LanguageTypeEnum.ENUS.getKey().equalsIgnoreCase(lang)){
            result = buyerEmailTemplateDao.selectList(new LambdaQueryWrapper<BuyerEmailTemplateEntity>()
                    .eq(BuyerEmailTemplateEntity::getTemplateType, type)
                    .eq(BuyerEmailTemplateEntity::getLangCode, LanguageTypeEnum.ENUS.getKey()));
        }
        if (CollectionUtils.isNotEmpty(result)) {
            List<BuyerEmailTemplateVO> list = result.stream().map(t -> {
                BuyerEmailTemplateVO studentVO = new BuyerEmailTemplateVO();
                BeanUtils.copyProperties(t, studentVO);
                return studentVO;
            }).collect(Collectors.toList());
            return Result.success(list);
        }
        return null;
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "判断询盘是否有附件")
    @GetMapping(value = "/has-attachment/{threadId}")
    @Logging(businessId = "#threadId",businessType = "CORE-ATT-001", errorTag = "RFI-CORE-10019")
    public Result<Boolean> hasAttachment(@PathVariable String threadId) {
        int count = inquiryAllFileAttachmentDao.hasAttachment(threadId);
        log.info("inquiry hasAttachment threadId:{},count:{}",threadId,count);

        return Result.success(count > 0);
    }

    /**
     * InquiryAllItemController 迁过来的
     */
    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "保存询盘信息")
    @PostMapping(value = "/v1/save-list-item")
    @Logging(businessType = "CORE-10036", errorTag = "RFI-CORE-10036")
    public Result<Boolean> saveInquiryAllItem(@RequestBody InquireAllItemDTO inquireAllItemDTOS) {
        InquiryAllItemEntity inquiryAllItemEntity = new InquiryAllItemEntity();
        BeanUtils.copyProperties(inquireAllItemDTOS, inquiryAllItemEntity);
        return Result.success(inquiryAllItemService.save(inquiryAllItemEntity));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "判断thread是否已建立买卖家关联")
    @GetMapping(value = "/v1/has-buyer-supplier-link")
    @Logging(businessId = "#threadId", businessType = "CORE-10037", errorTag = "RFI-CORE-10037")
    public Result<Boolean> hasBuyerSupplierLink(@RequestParam String threadId) {
        return Result.success(inquiryBuyerStatusDao.selectCount(new LambdaQueryWrapper<InquiryBuyerStatusEntity>().eq(InquiryBuyerStatusEntity::getThreadId, threadId)) > 1);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "真的通过inquiryId查询InquireAllItem")
    @GetMapping(value = "/v2/get-items-by-inquiry-id")
    @Logging(businessId = "#inquiryId", businessType = "CORE-10038", errorTag = "RFI-CORE-10038")
    public Result<List<InquireAllItemVO>> getInquireAllItemListByInquiryId(@RequestParam String inquiryId) {
        List<InquiryAllItemEntity> list = inquiryAllItemDao.selectList(new LambdaQueryWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getInquiryId, inquiryId));

        list = Optional.ofNullable(list).orElse(Lists.newArrayList());
        List<InquireAllItemVO> dtoList = BeanUtil.copyPropertiesBatch(list, InquireAllItemVO.class);
        return Result.success(dtoList);
    }

    @ApiOperation(value = "获取N小时内未回复且未勾选upsell,未发送upsell营销邮件的inquiry id list")
    @GetMapping(value = "/get-upsell-id-with-un-reply")
    public Result<List<String>> getUpsellInquiryIdListWithUnReply(@RequestParam Integer upsellUnReplyMinute) {
        return Result.success(inquiryAllDao.getUpsellInquiryIdListWithUnReply(upsellUnReplyMinute,upsellUnReplyDayLimit));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "标记为已发送upsell营销邮件")
    @GetMapping(value = "/mark-upsell-edm-email-send")
    Result<Boolean> markUpsellEDMEmailSend(@RequestParam String inquiryId){
        LambdaUpdateWrapper<InquiryAllEntity> param = new LambdaUpdateWrapper<>();
        param.eq(InquiryAllEntity::getInquiryId, inquiryId).set(InquiryAllEntity::getUpsellEdmEmailSentFlag, true)
                .set(InquiryAllEntity::getLUpdDate,new Date());

        return Result.success(inquiryAllDao.update(null, param) > 0);
    }

    @ApiOperation(value = "用户点击邮件链接后提交一对多询盘")
    @GetMapping(value = "/convert-upsell")
    public Result<Integer> convertUpsell(@RequestParam String threadId,@RequestParam Long userId) {
        return inquireAllService.convertUpsell(threadId,userId);
    }

    @ApiOperation(value = "获取N小时内未回复且勾选convertRfq, 未完成转RFQ的inquiry id list")
    @GetMapping(value = "/convert-rfq/get-un-reply-id")
    public Result<List<String>> getUnReplyInquiryIdListWithConvertRfq(@RequestParam Integer upsellUnReplyMinute) {
        return Result.success(inquiryAllDao.getUnReplyInquiryIdListWithConvertRfq(upsellUnReplyMinute, upsellUnReplyMaxMinute));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "标记已完成转RFQ")
    @PostMapping(value = "/convert-rfq/mark-complete")
    public Result<Boolean> markConvertRfqComplete(@RequestParam String inquiryId) {
        LambdaUpdateWrapper<InquiryAllEntity> param = new LambdaUpdateWrapper<InquiryAllEntity>()
                .eq(InquiryAllEntity::getInquiryId, inquiryId)
                .eq(InquiryAllEntity::getConvertRfqCompleteFlag, false)
                .set(InquiryAllEntity::getConvertRfqCompleteFlag, true);
        return Result.success(inquiryAllDao.update(null, param) > 0);
    }

    @ApiOperation(value = "补buyer company name")
    @PostMapping(value = "/fix-buyer-company-name")
    public void fixBuyerCompanyName(@RequestBody Date date){
        inquiryAllService.fixBuyerCompanyName(date);
    }

    @ApiOperation(value = "match score")
    @GetMapping(value = "/calc-match-score")
    public void calcSupplierMatchScore(){
        inquiryAllService.calcSupplierMatchScore();
    }

    @ApiOperation(value = "获取推荐产品")
    @GetMapping("/recommend-product")
    public Result<List<RecommendProductInfoVO>> recommendProduct(@RequestParam Long productId){
        return Result.success(inquiryAllService.recommendProductList(productId));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "潜在商机-获取未DOI RFI列表(超过N天，未DOI验证的RFI)")
    @GetMapping(value = "/get-rfi-non-doi-list")
    public Result<List<String>> getRfiNonDoiList(@RequestParam int days, @RequestParam Date startDate) {
       return Result.success(inquiryAllDao.getRfiNonDoiList(days, startDate));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "潜在商机-标记潜在商机")
    @PostMapping(value = "/mark-potential-opportunity")
    public Result<Boolean> markPotentialOpportunity(@RequestParam String threadId) {
        return Result.success(inquiryAllDao.markPotentialOpportunity(threadId) > 0);
    }

    /**
     * @deprecated 配置移到rfi agg，有传参的接口不再使用
     * @param days
     * @return
     */
    @Deprecated
    @ApiOperation(value = "潜在商机-删除潜在商机（超过N天，标记为潜在商机，未转为正常询盘的RFI）")
    @PostMapping(value = "/delete-potential-opportunity")
    public Result<Boolean> deletePotentialOpportunity(@RequestParam int days) {
        return Result.success(inquiryAllDao.deletePotentialOpportunity(days) > 0);
    }

    @ApiOperation(value = "潜在商机-删除潜在商机（超过N天，标记为潜在商机，未转为正常询盘的RFI）")
    @PostMapping(value = "v1/delete-potential-opportunity")
    public Result<Boolean> deletePotentialOpportunity() {
        return Result.success(inquiryAllDao.deletePotentialOpportunity(deletePotentialOpportunityDays) > 0);
    }

    @ApiOperation(value = "潜在商机-指定时间内收到的潜在商机的供应商: 创建时间+N天，且标记为潜在商机")
    @PostMapping(value = "/potential-opportunity-supplier-list")
    public Result<List<Long>> potentialOpportunitySupplierList(@RequestBody SupplierPotentialOpportunitySummaryDTO dto) {
        List<Long> supplierIdList = new ArrayList<>();
        List<Long> list = inquiryAllDao.potentialOpportunitySupplierList(dto);
        List<Long> doiList = inquiryAllDao.potentialOpportunityDoiSupplierList(dto);
        if(CollectionUtils.isNotEmpty(list)){
            supplierIdList.addAll(list);
        }
        if(CollectionUtils.isNotEmpty(doiList)){
            supplierIdList.addAll(doiList);
        }
        return Result.success(supplierIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
    }

    @ApiOperation(value = "潜在商机-指定时间内收到的潜在商机的新询盘汇总: 创建时间+N天，且标记为潜在商机")
    @PostMapping(value = "/potential-opportunity-summary-list")
    public Result<List<SupplierPotentialOpportunitySummaryVO>> potentialOpportunitySummaryList(@RequestBody SupplierPotentialOpportunitySummaryDTO dto) {
        return Result.success(inquiryAllDao.potentialOpportunitySummaryList(dto));
    }

    @ApiOperation(value = "潜在商机-指定时间内通过了DOI的潜在商机询盘汇总: doi_date，且标记为潜在商机，且doi_status=true，且未删除")
    @PostMapping(value = "/potential-opportunity-doi-summary-list")
    public Result<List<SupplierPotentialOpportunitySummaryVO>> potentialOpportunityDoiSummaryList(@RequestBody SupplierPotentialOpportunitySummaryDTO dto) {
        return Result.success(inquiryAllDao.potentialOpportunityDoiSummaryList(dto));
    }

    @ApiOperation(value = "发送SourcingClubMQ")
    @PostMapping(value = "/send-sourcing-club-mq")
    public void sendSourcingClubMQ(@RequestBody InquirySourcingClubMQDTO dto) {
        inquiryAllService.sendSourcingClubMQ(dto.getInquiryId(),dto.getBuyerId(),dto.getRfiStatus(),dto.getPostDate(),dto.getQcDate());
    }

    @ApiOperation(value = "保存eblock审核reason及是否导出supplier center")
    @PostMapping(value = "/update-eblock-reason")
    public Result<Integer> updateEblockAuditReason(@RequestBody InquiryUpdateEblockReasonDTO dto) {
        List<InquiryAllItemEntity> list = inquiryAllItemDao.selectList(new LambdaQueryWrapper<InquiryAllItemEntity>()
                .eq(InquiryAllItemEntity::getInquiryId, dto.getInquiryId()));
        if(CollectionUtils.isEmpty(list)){
            return Result.success(0);
        }
        String threadId = list.get(0).getThreadId();
        int count = inquiryAllItemDao.update(null,new LambdaUpdateWrapper<InquiryAllItemEntity>()
                .eq(InquiryAllItemEntity::getThreadId,threadId)
                .set(InquiryAllItemEntity::getAuditReason,dto.getAuditReason())
                .set(InquiryAllItemEntity::getExportToScFlag,dto.isExportToScFlag()));
        log.info("updateEblockAuditReason threadId : {} ,dto:{},count:{}",threadId,dto,count);
        return Result.success(count);
    }

    @ApiOperation(value = "获取询盘产品供应商聊天信息")
    @GetMapping(value = "/get-chat-info")
    public Result<InquiryChatInfoVO> getChatInfo(@RequestParam String threadId) {
        return Result.success(inquiryAllService.getChatInfo(threadId));
    }

    @PostMapping(value = "/inquiry-pin")
    @ApiOperation(value = "询盘置顶")
    public Result<Boolean> inquiryPin(@RequestBody InquiryPinDTO dto){
        return inquiryAllService.inquiryPin(dto);
    }


    @ApiOperation(value = "用户提交Rfi成功数量")
    @GetMapping(value = "/buyer-rfi-num/{buyerId}")
    @Logging(businessId = "#buyerId",businessType = "CORE-10053", errorTag = "RFI-CORE-10053")
    public Result<Integer> buyerRfiNum(@PathVariable Long buyerId) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DATE, -90);

        Integer count = inquiryAllDao.selectCount(new LambdaQueryWrapper<InquiryAllEntity>().eq(InquiryAllEntity::getBuyerId, buyerId)
                .eq(InquiryAllEntity::getFormCompleteStatus, "Y").gt(InquiryAllEntity::getCreateDate,cal.getTime())
        );
        return Result.success(count);
    }

}
