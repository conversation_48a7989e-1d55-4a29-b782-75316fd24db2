/**
 * <a>Title: SupplierAlertUpCompanyInfo </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/6/30-20:17
 */
package com.globalsources.framework.email.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierAlertUpCompanyInfo implements Serializable {

    @ApiModelProperty(value = "公司video更新日期：company video updated on 06/20:(06/20)")
    private String updateDate;

    @ApiModelProperty(value = "feeds 视频数量")
    private Integer feedsNum;

    @ApiModelProperty(value = "证书数量")
    private Integer certificationNum;
}
