package com.globalsources.framework.utils; /**
 * $Header: /data/cvs/snark/src/java/com/gsol/cuckoo/util/im/TLSUtil.java,v 1.2 2018/05/09 02:27:40 janeliu Exp $
 * $Revision: 1.2 $
 * $Date: 2018/05/09 02:27:40 $
 * ====================================================================
 *
 * Copyright (c) 2001 Media Data Systems Pte Ltd All Rights Reserved.
 * This software is the confidential and proprietary information of
 * Media Data Systems Pte Ltd.You shall not disclose such Confidential
 * Information.
 *
 * ====================================================================
 *
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;

import java.io.CharArrayReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Security;
import java.security.Signature;
import java.util.Arrays;
import java.util.zip.Deflater;


/**
 * TLS util class
 * 
 * <AUTHOR> janeliu $
 * @version $Revision: 1.2 $ $Date: 2018/05/09 02:27:40 $
 */
@Slf4j
@UtilityClass
public class TLSUtil {
	/**
	 * signature for an account
	 * 
	 * @param sdkAppId
	 * @param identifier
	 * @param privateKey
	 * @return
	 */
	public static String signature(String sdkAppId, String identifier, String privateKey) {
		String result = signatureEx(sdkAppId, identifier, privateKey);
		if (Strings.isNullOrEmpty(result)) {
			log.error("signature {} with private key on SDK App ID {} failed", identifier, sdkAppId);
		}
		return result;
	}

	private static String signatureEx(String sdkAppId, String identifier, String privateKey) {
		return signatureEx(sdkAppId, identifier, privateKey, 3600L * 24 * 180);
	}

	private static String signatureEx(String sdkAppId, String identifier, String privateKey, long expireTime) {
		String retVal = "";
		try {
			Security.addProvider(new BouncyCastleProvider());
			Reader reader = new CharArrayReader(privateKey.toCharArray());
			JcaPEMKeyConverter converter = new JcaPEMKeyConverter();
			PEMParser parser = new PEMParser(reader);
			Object obj = parser.readObject();
			parser.close();
			PrivateKey privKeyStruct = converter.getPrivateKey((PrivateKeyInfo) obj);

			String jsonString = "{" + "\"TLS.account_type\":\"" + 0 + "\"," + "\"TLS.identifier\":\"" + identifier
					+ "\"," + "\"TLS.appid_at_3rd\":\"" + 0 + "\"," + "\"TLS.sdk_appid\":\"" + sdkAppId + "\","
					+ "\"TLS.expire_after\":\"" + expireTime + "\"," + "\"TLS.version\": \"************\"" + "}";

			String time = String.valueOf(System.currentTimeMillis() / 1000);
			String serialString = "TLS.appid_at_3rd:" + 0 + "\n" + "TLS.account_type:" + 0 + "\n" + "TLS.identifier:"
					+ identifier + "\n" + "TLS.sdk_appid:" + sdkAppId + "\n" + "TLS.time:" + time + "\n"
					+ "TLS.expire_after:" + expireTime + "\n";

			//Create Signature by SerialString
			Signature signature = Signature.getInstance("SHA256withECDSA", "BC");
			signature.initSign(privKeyStruct);
			signature.update(serialString.getBytes(StandardCharsets.UTF_8));
			byte[] signatureBytes = signature.sign();

			String sigTLS = Base64.encodeBase64String(signatureBytes);

			//Add TlsSig to jsonString
			JSONObject jsonObject = JSON.parseObject(jsonString);
			jsonObject.put("TLS.sig",  sigTLS);
			jsonObject.put("TLS.time", time);
			jsonString = jsonObject.toString();

			//compression
			Deflater compresser = new Deflater();
			compresser.setInput(jsonString.getBytes(StandardCharsets.UTF_8));

			compresser.finish();
			byte[] compressBytes = new byte[512];
			int compressBytesLength = compresser.deflate(compressBytes);
			compresser.end();
			String userSig = new String(Base64URL.base64EncodeUrl(Arrays.copyOfRange(compressBytes, 0,
					compressBytesLength)));

			retVal = userSig;
		} catch (Exception e) {
			log.error("signature error", e);
		}

		return retVal;
	}

	/**
	 * Base64 encoding
	 * 
	* <AUTHOR> janeliu $
	* @version $Revision: 1.2 $ $Date: 2018/05/09 02:27:40 $
	 */
	@UtilityClass
	static class Base64URL {
		static byte[] base64TableUrl = { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O',
				'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i',
				'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2',
				'3', '4', '5', '6', '7', '8', '9', '*', '-', '\0' };

		static byte base64PadUrl = '_';

		static short[] base64ReverseTableUrl = { -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
				-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62,
				-1, -1, 63, -1, -1, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4,
				5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1,
				26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51,
				-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
				-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
				-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
				-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
				-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
				-1, -1, -1 };

		/**
		 * un signed to bytes
		 * 
		 * @param b
		 * @return
		 */
		public static int unsignedToBytes(int b) {
			return b & 0xFF;
		}

		/**
		 * base64 encode url
		 * 
		 * @param inStr
		 * @return
		 */
		public static byte[] base64EncodeUrl(byte[] inStr) {
			byte[] outStr = new byte[1024];

			int outCurrent = 0;
			int current = 0;
			int length = inStr.length;

			while (length > 2) { /* keep going until we have less than 24 bits */

				outStr[outCurrent++] = base64TableUrl[unsignedToBytes((unsignedToBytes(inStr[current]) >>> 2))];
				outStr[outCurrent++] = base64TableUrl[unsignedToBytes(unsignedToBytes(unsignedToBytes(inStr[current]) & 0x03) << 4)
						+ unsignedToBytes((unsignedToBytes(inStr[current + 1]) >>> 4))];
				outStr[outCurrent++] = base64TableUrl[(unsignedToBytes((unsignedToBytes(inStr[current + 1]) & 0x0f)) << 2)
						+ unsignedToBytes((unsignedToBytes(inStr[current + 2]) >>> 6))];
				outStr[outCurrent++] = base64TableUrl[unsignedToBytes((unsignedToBytes(inStr[current + 2]) & 0x3f))];
				current += 3;
				length -= 3; /* we just handle 3 octets of data */
			}

			/* now deal with the tail end of things */
			if (length != 0) {
				outStr[outCurrent++] = base64TableUrl[unsignedToBytes(inStr[current]) >>> 2];
				if (length > 1) {
					outStr[outCurrent++] = base64TableUrl[unsignedToBytes((unsignedToBytes(inStr[current]) & 0x03) << 4)
							+ unsignedToBytes(unsignedToBytes(inStr[current + 1]) >>> 4)];
					outStr[outCurrent++] = base64TableUrl[unsignedToBytes((unsignedToBytes(inStr[current + 1]) & 0x0f) << 2)];
					outStr[outCurrent++] = base64PadUrl;
				} else {
					outStr[outCurrent++] = base64TableUrl[unsignedToBytes((unsignedToBytes(inStr[current]) & 0x03) << 4)];
					outStr[outCurrent++] = base64PadUrl;
					outStr[outCurrent++] = base64PadUrl;
				}
			}

			byte[] outBytes = new String(outStr).getBytes();
			return Arrays.copyOfRange(outBytes, 0, outCurrent);
		}

		/**
		 * base64 decode url
		 * 
		 * @param inStr
		 * @return
		 */
		public static byte[] base64DecodeUrl(byte[] inStr) {
			int ch, i = 0, j = 0, k;

			int current = 0;
			byte[] outStr = new byte[1024];
			int length = inStr.length;
			/* this sucks for threaded environments */

			/* run through the whole string, converting as we go */
			ch = inStr[0];
			while (length-- > 0) {
				ch = inStr[current++];
				if (ch == base64PadUrl)
					break;
				/* When Base64 gets POSTed, all pluses are interpreted as spaces.
				   This line changes them back.  It's not exactly the Base64 spec,
				   but it is completely compatible with it (the spec says that
				   spaces are invalid).  This will also save many people considerable
				   headache.  - Turadg Aleahmad <<EMAIL>>
				*/
				if (ch == ' ')
					ch = '*'; //never using '+'

				ch = base64ReverseTableUrl[ch];
				if (ch < 0)
					continue;

				switch (i % 4) {
				case 0:
					outStr[j] = (byte) unsignedToBytes(unsignedToBytes(ch) << 2);
					break;
				case 1:
					outStr[j++] |= (byte) unsignedToBytes(unsignedToBytes(ch) >>> 4);
					outStr[j] = (byte) unsignedToBytes(unsignedToBytes(unsignedToBytes(ch) & 0x0f) << 4);
					break;
				case 2:
					outStr[j++] |= (byte) unsignedToBytes(unsignedToBytes(ch) >>> 2);
					outStr[j] = (byte) unsignedToBytes(unsignedToBytes(unsignedToBytes(ch) & 0x03) << 6);
					break;
				case 3:
					outStr[j++] |= (byte) unsignedToBytes(ch);
					break;
				default:
				}
				i++;
			}
			k = j;
			/* mop things up if we ended on a boundary */
			if (ch == base64PadUrl) {
				switch (i % 4) {
				case 0:
				case 1:
					byte[] error = new byte[1];
					error[0] = '\0';
					return error;
				case 2:
					k++;
				case 3:
					outStr[k++] = 0;
				default:
				}
			}
			return Arrays.copyOfRange(outStr, 0, j);
		}
	}
}
