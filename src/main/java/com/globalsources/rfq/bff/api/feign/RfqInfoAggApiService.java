//package com.globalsources.rfq.bff.api.feign;
//
//import com.globalsources.framework.page.BasePage;
//import com.globalsources.framework.result.PageResult;
//import com.globalsources.framework.result.Result;
//import com.globalsources.rfq.bff.api.model.dto.es.RfqBffIndexDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqAdminEditRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqAppCreateRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqBookMarkDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqCloseRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqHighSeaRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqInSiteNotificationRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqInfoCreateRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqInfoListRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqMobileCreateRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqMobileInfoRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqReadStatusRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqSellerDataAnalysisRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqSellerDeleteRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqSellerHighSeaRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqStarListRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqSupplierDetailRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqSupplierReadStatusRequestDTO;
//import com.globalsources.rfq.bff.api.model.dto.req.RfqSupplierRequestDTO;
//import com.globalsources.rfq.bff.api.model.vo.CategorySuggestVO;
//import com.globalsources.rfq.bff.api.model.vo.ProductCategoryAttributeVO;
//import com.globalsources.rfq.bff.api.model.vo.RfqHighSeaCarouselListVO;
//import com.globalsources.rfq.bff.api.model.vo.RfqHighSeaListVO;
//import com.globalsources.rfq.bff.api.model.vo.RfqInfoListVO;
//import com.globalsources.rfq.bff.api.model.vo.RfqMobileInfoVO;
//import com.globalsources.rfq.bff.api.model.vo.admin.RfaAdminListDTO;
//import com.globalsources.rfq.bff.api.model.vo.admin.RfqAdminConsoleDetailVO;
//import com.globalsources.rfq.bff.api.model.vo.admin.RfqAdminDetailVO;
//import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppInfoVO;
//import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppNumsVO;
//import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppQuotationDetailVO;
//import com.globalsources.rfq.bff.api.model.vo.app.SupplierAppRfqDetailInfoVO;
//import com.globalsources.rfq.bff.api.model.vo.app.SupplierAppRfqListVO;
//import com.globalsources.rfq.bff.api.model.vo.app.SupplierAppRfqRequestDTO;
//import com.globalsources.rfq.bff.api.model.vo.review.RfqReviewDetailVO;
//import com.globalsources.rfq.bff.api.model.vo.review.RfqReviewListVO;
//import com.globalsources.rfq.bff.api.model.vo.review.RfqUserReviewVO;
//import com.globalsources.rfq.bff.api.model.vo.seller.RfqBookMarkVO;
//import com.globalsources.rfq.bff.api.model.vo.seller.SellerRfqDataAnalysisVO;
//import com.globalsources.rfq.bff.api.model.vo.seller.SellerRfqDetailVO;
//import com.globalsources.rfq.bff.api.model.vo.seller.SellerRfqHighSeaVO;
//import com.globalsources.rfq.bff.api.model.vo.seller.SellerRfqListVO;
//import com.globalsources.rfq.bff.api.model.vo.seller.SellerRfqPageVO;
//import com.globalsources.rfq.core.api.model.dto.RfqAdminListDTO;
//import com.globalsources.rfq.core.api.model.dto.RfqReviewRequestDTO;
//import com.globalsources.rfq.core.api.model.dto.app.RfqAppInfoListRequestDTO;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import javax.validation.Valid;
//import java.util.List;
//
///**
// * <a>Title: RfqInfoAggApiService </a>
// * <a>Author: Mike Chen <a>
// * <a>Description: RfqInfoAggApiService <a>
// *
// * <AUTHOR> Chen
// * @date 2021/11/26 15:55
// */
//@FeignClient(name = "gsol-rfq-agg", path = "/rfq-agg")
//public interface RfqInfoAggApiService {
//
//    @PostMapping("v1/buyer/rfq-list")
//    PageResult<RfqInfoListVO> getRfqList(@Valid @RequestBody RfqInfoListRequestDTO requestDTO);
//
//    @PostMapping("v1/rfq-create")
//    Result createRfqInfo(@Valid @RequestBody RfqInfoCreateRequestDTO requestDTO);
//
//    @GetMapping("v1/rfq-unread-count/{userId}")
//    Integer rfqUnReadCount(@PathVariable("userId") Long userId);
//
//    @PostMapping("v1/buyer/rfq-read-status")
//    boolean rfqReadStatus(@Valid @RequestBody RfqReadStatusRequestDTO requestDTO);
//
//    @PostMapping("v1/rfq-close")
//    boolean rfqClose(@Valid @RequestBody RfqCloseRequestDTO requestDTO);
//
//    @PostMapping("v1/buyer/rfq-high-sea")
//    PageResult<RfqHighSeaListVO> rfqHighSea(@Valid @RequestBody RfqHighSeaRequestDTO requestDTO);
//
//    @PostMapping("v1/rfq-high-sea-carousel")
//    PageResult<RfqHighSeaCarouselListVO> rfqHighSeaCarousel(@Valid @RequestBody BasePage basePage);
//
//    @GetMapping("v1/rfq-turnover-statistics/{userId}")
//    Integer rfqQuotationTurnoverStatistics(@PathVariable("userId") Long userId);
//
//    @PostMapping("v1/buyer/star-list")
//    PageResult<RfqInfoListVO> rfqStarList(@Valid @RequestBody RfqStarListRequestDTO requestDTO);
//
//    @GetMapping("v1/category-attribute/{l4CategoryId}")
//    List<ProductCategoryAttributeVO> getCategoryAttribute(@PathVariable("l4CategoryId") Long l4CategoryId);
//
//
//    @PostMapping("v1/seller/rfq-read-status")
//    Boolean sellerRfqReadStatus(@Valid @RequestBody RfqSupplierReadStatusRequestDTO requestDTO);
//
//    @PostMapping("v1/seller/rfq-high-sea")
//    SellerRfqHighSeaVO sellerRfqHighSea(@Valid @RequestBody RfqSellerHighSeaRequestDTO requestDTO);
//
//    @PostMapping("v1/rfq-detail")
//    Result<SellerRfqDetailVO> sellerRfqDetail(@Valid @RequestBody RfqSupplierDetailRequestDTO requestDTO);
//
//    @PostMapping("v1/seller/star-list")
//    PageResult<SellerRfqListVO> sellerRfqStarList(RfqStarListRequestDTO requestDTO);
//
//    @GetMapping("v1/sync-rfq-index")
//    List<RfqBffIndexDTO> getRfqIndex(@RequestBody BasePage basePage);
//
//    @GetMapping("v1/sync-rfq-count")
//    Result<Integer> getAllSyncCount();
//
//    @GetMapping("v1/buyer/mobile-list")
//    PageResult<RfqMobileInfoVO> getRfqMobileInfoList(@Valid @RequestBody RfqMobileInfoRequestDTO requestDTO);
//
//    @PostMapping("v1/buyer/rfq-mobile-create")
//    Result createMobileRfqInfo(@Valid @RequestBody RfqMobileCreateRequestDTO requestDTO);
//
//    @GetMapping("v1/seller/in-site-notification")
//    Result inSiteNotification(@Valid @RequestBody RfqInSiteNotificationRequestDTO requestDTO);
//
//    @GetMapping("/v1/seller/count/{supplierId}")
//    Result<Integer> getRfqThreadCnt(@PathVariable("supplierId") Long supplierId, @RequestParam("days") Integer days);
//
//    @GetMapping("v1/seller/rfq-unread-count/{supplierId}")
//    Result<Integer> getSupplierRfqUnReadCount(@PathVariable("supplierId") Long supplierId);
//
//    @GetMapping("v1/seller/receive/latest/{supplierId}")
//    Result<SellerRfqListVO> getLatestRfqOfSeller(@PathVariable("supplierId") Long supplierId);
//
//    @PostMapping("v1/buyer/rfq-app-create")
//    Result createAppRfqInfo(RfqAppCreateRequestDTO requestDTO);
//
//    @PostMapping("v1/buyer/app-list")
//    Result<PageResult<RfqBuyerAppInfoVO>> getRfqBuyerAppInfoList(@Valid @RequestBody RfqAppInfoListRequestDTO requestDTO);
//
//    @GetMapping("v1/buyer/app-list-nums")
//    Result<RfqBuyerAppNumsVO> getRfqBuyerAppInfoListNums(@RequestParam Long userId);
//
//    @GetMapping("v1/buyer/app-detail")
//    Result<RfqBuyerAppQuotationDetailVO> getRfqBuyerAppDetail(@RequestParam String rfqId);
//
//    @PostMapping("v1/seller/data-analysis")
//    Result<SellerRfqDataAnalysisVO> getSellerDataAnalysis(@Valid @RequestBody RfqSellerDataAnalysisRequestDTO requestDTO);
//
//    @GetMapping("v1/admin-console-platform-detail/{rfqId}")
//    Result<RfqAdminConsoleDetailVO> getAdminConsoleRfqDetail(@PathVariable("rfqId") String rfqId);
//
//    @GetMapping("v1/admin-console-detail/{rfqId}")
//    Result<RfqAdminDetailVO> getAdminRfqDetail(@PathVariable("rfqId") String rfqId);
//
//    @PostMapping("v1/admin-console-list")
//    PageResult<RfaAdminListDTO> getAdminRfqList(@Valid @RequestBody RfqAdminListDTO requestDTO);
//
//    @PostMapping("v1/edit-rfq")
//    Result editRfq(@RequestBody RfqAdminEditRequestDTO rfq);
//
//    @PostMapping("v1/expire-rfq-batch")
//    Result expireRfqBatch(@RequestBody List<String> rfqIdList, @RequestParam Long reviewerId);
//
//    @PostMapping("v1/delete-rfq-batch")
//    Result deleteRfqBatch(@RequestBody List<String> rfqIdList, @RequestParam Long reviewerId);
//
//    @GetMapping("v1/review/action")
//    Result<Boolean> review(@RequestParam Long reviewerId, @RequestParam Integer actionValue, @RequestParam String rfqId);
//
//    @GetMapping("v1/review/pending-review-count")
//    Result<Integer> getPendingReviewCount();
//
//    @GetMapping("v1/review/detail")
//    Result<RfqReviewDetailVO> detail(@RequestParam String rfqId);
//
//    @PostMapping("v1/review/review-list-by-user")
//    Result<RfqUserReviewVO> getReviewListByUser(@RequestBody RfqReviewRequestDTO rfqReviewPageDTO);
//
//    @PostMapping("v1/review/list")
//    Result<PageResult<RfqReviewListVO>> list(@RequestBody RfqReviewRequestDTO rfqReviewPageDTO);
//
//    @PostMapping("/v1/supplier-app-list")
//    Result<SupplierAppRfqListVO> getSupplierAppRfqInfoList(@Valid @RequestBody SupplierAppRfqRequestDTO requestDTO);
//
//    @GetMapping(value = "v1/supplier-detail")
//    Result<SupplierAppRfqDetailInfoVO> supplierRfqDetail(@RequestParam Long supplierId, @RequestParam String rfqId);
//
//    @Deprecated
//    @GetMapping("v1/sync-rfq/{rfqId}")
//    void syncIndex(@PathVariable("rfqId") String rfqId);
//
//    @GetMapping("V1/rfq-star-unread-count/{userId}")
//    Integer rfqStarUnReadCount(@PathVariable("userId") Long userId);
//
//    @GetMapping(value = "v1/category-suggest/{productName}/{locale}")
//    Result<List<CategorySuggestVO>> categorySuggest(@PathVariable("productName") String productName,
//                                                    @PathVariable("locale") String locale);
//
//    @PostMapping(value = "v1/rfq-book-mark-num")
//    RfqBookMarkVO rfqBookMarkNum(@RequestBody RfqBookMarkDTO rfqBookMarkDTO);
//
//    @PostMapping("v1/seller/delete")
//    Result<Boolean> delete(@Valid @RequestBody RfqSellerDeleteRequestDTO requestDTO);
//
//    @PostMapping("v1/seller/delete-rfq-list")
//    PageResult<SellerRfqListVO> deleteRfqList(@RequestBody RfqStarListRequestDTO requestDTO);
//
//    @PostMapping("v1/seller/delete-recover/{userId}/{supplierId}/{rfqIds}")
//    Result<Boolean> deleteRecover(@PathVariable("userId") Long userId,
//                                  @PathVariable("supplierId") Long supplierId,
//                                  @PathVariable("rfqIds") List<String> rfqIds);
//
//}
