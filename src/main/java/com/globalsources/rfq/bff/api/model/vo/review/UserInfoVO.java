package com.globalsources.rfq.bff.api.model.vo.review;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoVO  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(
            value = "用户id",
            required = false
    )
    private Long userId;

    @ApiModelProperty(
            value = "用户头像",
            required = false
    )
    private String photo;

    @ApiModelProperty(
            value = "用户名称",
            required = false
    )
    private String firstName;
    @ApiModelProperty(
            value = "和户姓氏",
            required = false
    )
    private String lastName;


    @ApiModelProperty(
            value = "email",
            required = false
    )
    private String email;


    @ApiModelProperty(
            value = "国家码",
            required = false
    )
    private String countryCode;

    @ApiModelProperty(
            value = "国家英文名称",
            required = false
    )
    private String countryName;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty(
            value = "公司名称",
            required = false
    )
    private String companyName;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty(
            value = "创建日期",
            required = false
    )
    private Date createDate;

}
