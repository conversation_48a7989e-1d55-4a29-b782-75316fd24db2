package com.globalsources.rfq.bff.api.feign;

import com.globalsources.framework.result.Result;
import com.globalsources.model.bff.dto.page.external.RfqCountDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * RFQ对外服务接口
 */
@FeignClient(name = "gsol-rfq-agg", path = "/rfq-agg/external-service")
public interface RfqAggExternalService {

    @ApiOperation(value = "待报价数量(最近1年)")
    @PostMapping("v1/un-quotation-count")
    Result<Integer> getUnQuotationCount(@Valid @RequestBody RfqCountDTO rfqCountDTO);

    @ApiOperation(value = "返回有报价的供应商id列表")
    @PostMapping("v1/quotation-supplier-list")
    Result<List<Long>> quotationSupplierList(@RequestBody List<Long> supplierIdList);

}
