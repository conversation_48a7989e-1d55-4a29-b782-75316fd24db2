package com.globalsources.rfq.bff.api.model.dto.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: RfqQuotationChatRepliedRequestDTO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR>
 * @date 2021/8/23 10:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class RfqQuotationChatRepliedRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long supplierId;

    private Long userId;

    private String rfqId;

    private String quotationId;
}
