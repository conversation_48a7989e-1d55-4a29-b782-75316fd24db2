package com.globalsources.rfq.bff.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: CategorySuggestVO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: CategorySuggestVO <a>
 *
 * <AUTHOR>
 * @date 2021/10/14 16:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ApiModel(value = "rfq买家端产品类目联想")
public class CategorySuggestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "联想类目l4")
    private String suggestCategoryL4En;

    @ApiModelProperty(value = "格式化联想类目l2 >> l3 >> l4")
    private String suggestCategoryEn;

    @ApiModelProperty(value = "产品类目l1 id")
    private Long categoryL1;

    @ApiModelProperty(value = "产品类目l2 id")
    private Long categoryL2;

    @ApiModelProperty(value = "产品类目l3 id")
    private Long categoryL3;

    @ApiModelProperty(value = "产品类目l4 id")
    private Long categoryL4;
}
