package com.globalsources.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.admin.feign.ISearchFeign;
import com.globalsources.admin.model.dto.search.request.ISearchRequest;
import com.globalsources.admin.model.dto.search.response.ISearchResponse;
import com.globalsources.admin.service.SearchService;
import com.globalsources.framework.result.Result;
import com.globalsources.rfx.model.RfqThreadBO;
import com.globalsources.rfx.query.PageRequest;
import com.globalsources.rfx.service.ISearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <a>Title: SearchServiceImpl </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR>
 * @date 2021/7/19 10:16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SearchServiceImpl implements SearchService {

    private final ISearchFeign searchFeign;

    private static final Integer RFQ_MATCH_COUNT = 30;

    private final ISearchService iSearchService;

    @Override
    public List<Long> getRfqMatchedSupplierId(String productName, Long l4CategoryId) {
        int page = 1;

        // 获取供应商
        boolean right = true;
        List<Long> supplierList = new ArrayList<>();
        RfqThreadBO rfqThreadBO =null;
        while (right) {
            rfqThreadBO = iSearchService.rfqThreadMatch(productName, l4CategoryId,null,null, PageRequest.of(page, 50));

            if (Objects.isNull(rfqThreadBO) || CollectionUtils.isEmpty(rfqThreadBO.getSupplierIdList())) {
                break;
            }
            supplierList.addAll(rfqThreadBO.getSupplierIdList());

            if (supplierList.size() >= RFQ_MATCH_COUNT) {
                right = false;
            }
            page++;
        }
        return supplierList.subList(0,Math.min(supplierList.size(), RFQ_MATCH_COUNT));
    }

    @Override
    public Result<ISearchResponse> genericSearchWithObjectAndBusinessType(ISearchRequest searchRequest, @NotNull String businessType) {
        Result<ISearchResponse> iSearchResponseResult = null;
        if (Objects.nonNull(searchRequest)) {
            try {
                iSearchResponseResult = searchFeign.genericSearchWithObjectAndBusinessType(searchRequest, businessType);
            } catch (Exception e) {
                log.error("failed to genericSearchWithObjectAndBusinessType, errorMsg: " + e.getMessage() + " , businessType: " + businessType + " , searchRequest: " + JSON.toJSONString(searchRequest), e);
                throw e;
            }
        } else {
            log.warn("genericSearchWithObjectAndBusinessType, searchRequest is null");
        }
        return iSearchResponseResult;
    }
}
