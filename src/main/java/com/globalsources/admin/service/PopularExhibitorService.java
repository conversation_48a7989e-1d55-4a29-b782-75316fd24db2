package com.globalsources.admin.service;

import com.globalsources.activity.agg.api.dto.ReviewDTO;
import com.globalsources.activity.core.api.dto.BestSupplierDTO;
import com.globalsources.activity.core.api.dto.SearchBestSupplierDTO;
import com.globalsources.admin.model.vo.LiveBestSupplierDetailVO;
import com.globalsources.admin.model.vo.LiveBestSupplierVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;

import java.util.List;

public interface PopularExhibitorService {
    Result<PageResult<LiveBestSupplierVO>> search(SearchBestSupplierDTO searchDTO);

    Result<Void> save(BestSupplierDTO dto);

    Result<LiveBestSupplierDetailVO> getInfo(Long bestSuppId);

    Result<Void> delete(Long userId, Long bestSuppId);

    Result<Void> review(ReviewDTO reviewDTO);

    Result<Void> switchEnabled(Long userId, Long bestSuppId, Boolean enabled);

    List<LiveBestSupplierVO> export(SearchBestSupplierDTO searchDTO);

    Result<Void> assignTo(Long userId, Long assignedUserId, Long bestSuppId);
}
