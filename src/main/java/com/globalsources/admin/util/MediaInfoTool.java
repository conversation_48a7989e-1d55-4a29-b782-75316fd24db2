/**
 * $Header: /data/cvs/snark/src/java/com/gsol/cuckoo/util/common/VideoUtil.java,v 1.7 2021/01/27 02:01:04 janeliu Exp $
 * $Revision: 1.7 $
 * $Date: 2021/01/27 02:01:04 $
 *
 * ====================================================================
 *
 * Copyright (c) 2001 Media Data Systems Pte Ltd All Rights Reserved.
 * This software is the confidential and proprietary information of
 * Media Data Systems Pte Ltd.You shall not disclose such Confidential
 * Information.
 *
 * ====================================================================
 *
 */
package com.globalsources.admin.util;

import it.sauronsoftware.jave.Encoder;
import it.sauronsoftware.jave.FFMPEGLocator;
import it.sauronsoftware.jave.MultimediaInfo;
import it.sauronsoftware.jave.VideoInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;


/**
 * 
 * get video info 
 * Creation Date and by: 2017/08/11 Author:hadisli
 * <AUTHOR> janeliu $
 * @version $Revision: 1.7 $ $Date: 2021/01/27 02:01:04 $
 * 
 */
@Slf4j
@Component
public class MediaInfoTool {

	/** video analysis tool place */
	//@Value("${video.analysis.tool.place:D:/server/ffmpeg/bin/ffmpeg}")
	@Value("${video.analysis.tool.place:/usr/local/bin/ffmpeg}")  //app_param:VIDEO_ANALYSIS_TOOL_PLACE:/usr/local/ffmpeg/bin/ffmpeg
	private String videoAnalysisToolPlace;
	
	private final Encoder encoder = new Encoder(new FFMPEGLocator() {
		@Override
		protected String getFFMPEGExecutablePath() {
			return videoAnalysisToolPlace;
		}
	});
	
	/**
	 * get video info by file
	 * @param file file object
	 * @return
	 * @throws Exception
	 */
	public MultimediaInfo getMediaInfo(File file) throws Exception{
		log.info("{} -i {}", videoAnalysisToolPlace, file.getAbsolutePath());
		return encoder.getInfo(file);
	}

	/**
	 * 	return video properties
	 * @param file video file.
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> getVideoPropertits(File file) throws Exception{
		// analysis video
		MultimediaInfo mediaInfo = getMediaInfo(file);
		VideoInfo videoInfo = mediaInfo.getVideo();

		Map<String,Object> videoProperties=new HashMap<>();
		videoProperties.put("FrameHeight", videoInfo.getSize().getHeight());
		videoProperties.put("FrameWidth", videoInfo.getSize().getWidth());
		videoProperties.put("VideoResolution", getResolution(videoInfo.getSize().getWidth(), videoInfo.getSize().getHeight()));
		videoProperties.put("VideoSize", file.length());
		videoProperties.put("PlayTime", Double.parseDouble(new DecimalFormat("#.00").format(mediaInfo.getDuration() / (1000.0))));
		videoProperties.put("TotalBitRate", (videoInfo.getBitRate() + (mediaInfo.getAudio() == null ? 0 : mediaInfo.getAudio().getBitRate())));
		videoProperties.put("VideoDecoder", StringUtils.substringBefore(videoInfo.getDecoder(), " "));
		videoProperties.put("AudioDecoder", mediaInfo.getAudio() == null ? "" : StringUtils.substringBefore(mediaInfo.getAudio().getDecoder(), " "));
		log.info("video {}, videoProperties is {}", file.getName(), videoProperties);
		return videoProperties;
	}

	/**
	 * get video/image resolution
	 * @param width
	 * @param height
	 * @return
	 */
	public static String getResolution(int width, int height){
		int gcd = gcd(width, height);
		if(gcd == 0){
			return width+":"+height;
		}
		return width/gcd+":"+height/gcd;
	}
	
	/**
	 *  greatest common divisor
	 * @param width
	 * @param height
	 * @return
	 */
	public static int gcd(int width, int height) {
		if (width < 0 || height < 0)
			return -1;
		if (width == height || width == 0)
			return height;
		if (height == 0)
			return width;
		if (width < height)
			return gcd(width, height % width);
		else
			return gcd(height, width % height);
	}
}
