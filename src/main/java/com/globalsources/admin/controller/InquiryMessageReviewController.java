package com.globalsources.admin.controller;

import com.globalsources.admin.constants.OperationConstant;
import com.globalsources.admin.model.vo.SupplierInfoVO;
import com.globalsources.admin.model.vo.UserInfoVO;
import com.globalsources.admin.model.vo.inquiry.RfiMessageReviewDetailVO;
import com.globalsources.admin.model.vo.inquiry.RfiMessageUserReviewVO;
import com.globalsources.admin.service.SensitiveDetectService;
import com.globalsources.agg.supplier.api.feign.SupplierAggFeign;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.common.api.feign.CommonAggFeign;
import com.globalsources.common.api.vo.DictionaryItemVO;
import com.globalsources.common.api.vo.QueryDictListRequestVO;
import com.globalsources.common.api.vo.QueryDictListResultVO;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.constants.CoreConstants;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfi.agg.feign.MessageReviewFeign;
import com.globalsources.rfi.agg.request.message.PendingReviewMessageDTO;
import com.globalsources.rfi.agg.request.message.PendingReviewUserMessageDTO;
import com.globalsources.rfi.agg.request.message.ReviewMessageDTO;
import com.globalsources.rfi.agg.response.message.MessageReviewDetailVO;
import com.globalsources.rfi.agg.response.message.MessageReviewListVO;
import com.globalsources.rfi.agg.response.message.MessageUserReviewListVO;
import com.globalsources.rfq.bff.api.enums.ReviewActionEnum;
import com.globalsources.supplierconsole.agg.api.constant.ApplicationCode;
import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.log.dto.OperationLogDTO;
import com.globalsources.supplierconsole.agg.api.log.feign.OperationLogFeign;
import com.globalsources.user.api.feign.UserQueryFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rfi/message-review")
@Slf4j
@Api(tags = "rfi message审核")
public class InquiryMessageReviewController {
    @Autowired
    private MessageReviewFeign messageReviewFeign;

    @Autowired
    private UserQueryFeign userQueryFeign;

    @Autowired
    private SupplierAggFeign supplierAggFeign;
    @Autowired
    private OperationLogFeign operationLogFeign;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private CommonAggFeign commonAggFeign;

    @Autowired
    private SensitiveDetectService sensitiveDetectService;

    private static final String PRE = "<span class=highlight_yellow>";

    private static final String SUF = "</span>";

    private static final String COUNTRY="Country";

    private static final String EN="en";
    private static final String LATEST_SEND_DATE = "latestSendDate";
    private static final String ASC = "asc";
    private static final String SEND_DATE = "sendDate";
    @AdminLogin
    @ApiOperation(value = "全部待审核rfi message", notes = "全部待审核rfi message", tags = {"rfi-message审核"})
    @PostMapping("/v1/review-list")
    public Result<PageResult<MessageReviewListVO>> list(@ApiIgnore UserVO userVO, @RequestBody PendingReviewMessageDTO rfqReviewPageDTO) {
        if(Objects.isNull(rfqReviewPageDTO)){
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }
        if(StringUtils.isEmpty(rfqReviewPageDTO.getSortField())){
            rfqReviewPageDTO.setSortField(LATEST_SEND_DATE);
        }
        if(StringUtils.isEmpty(rfqReviewPageDTO.getSortType())){
            rfqReviewPageDTO.setSortType(ASC);
        }
        return messageReviewFeign.reviewList(rfqReviewPageDTO);
    }

    @ApiOperation(value = "获取单个用户待审核rfi message", notes = "获取单个用户待审核rfi message", tags = {"rfi-message审核"})
    @AdminLogin
    @PostMapping("/v1/user-review-list")
    public Result<RfiMessageUserReviewVO> getReviewListByUser(@ApiIgnore UserVO userVO, @RequestBody PendingReviewUserMessageDTO dto) {
        if(Objects.isNull(dto) || Objects.isNull(dto.getUserId())){
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }
        if(StringUtils.isEmpty(dto.getSortField())){
            dto.setSortField(SEND_DATE);
        }
        if(StringUtils.isEmpty(dto.getSortType())){
            dto.setSortType(ASC);
        }
        PageResult<MessageUserReviewListVO> pageResult= ResultUtil.getData(messageReviewFeign.getReviewListByUser(dto));

        return Result.success(RfiMessageUserReviewVO.builder().userInfo(getBuyerInfo(dto.getUserId())).data(pageResult).build());
    }

    @ApiOperation(value = "获取rfi message", notes = "获取rfi message", tags = {"rfi-message审核"})
    @AdminLogin
    @GetMapping("/v1/detail")
    public Result<RfiMessageReviewDetailVO> getReviewDetail(@ApiIgnore UserVO userVO, @RequestParam Long messageId) {
        if(Objects.isNull(messageId)){
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }
        MessageReviewDetailVO detailVO=ResultUtil.getData(messageReviewFeign.getMessageReviewDetail(messageId));
        if(Objects.isNull(detailVO)){
            return Result.failed(ResultCode.CommonResultCode.DATA_NON_EXISTENT);
        }
        if(StringUtils.isNotEmpty(detailVO.getMessage())){
            detailVO.setMessage(sensitiveDetectService.markSensitiveKey(detailVO.getMessage()));
            if(StringUtils.isNotEmpty(detailVO.getSuspiciousUrl())){
                String[] urls=detailVO.getSuspiciousUrl().split(",");
                for(String url:urls){
                    detailVO.setMessage(detailVO.getMessage().replace(url,PRE+url+SUF));
                }
            }
        }
        return Result.success(RfiMessageReviewDetailVO.builder()
                .userInfo(getBuyerInfo(detailVO.getBuyerId()))
                .detail(detailVO)
                .supplierInfo(getSupplierInfo(detailVO.getSupplierId(),detailVO.getSupplierUserId())).build());
    }

    @ApiOperation(value = "审核下拉框列表", notes = "审核下拉框列表", tags = {"rfi-message审核"})
    @GetMapping("/v1/action-list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "pass/block/reject", required = true, dataType = "String"),
    })
    public Result<List<Map<String, Object>>> getActionList(@RequestParam(value = "type") String type) {
        return Result.success(ReviewActionEnum.getEnumByType(type));
    }

    @AdminLogin
    @ApiOperation(value = "审核", notes = "审核 ", tags = {"rfi-message审核"})
    @GetMapping("/v1/review")
    public Result<Boolean> action(@ApiIgnore UserVO userVO,
                                  @RequestParam("actionValue") Integer actionValue,
                                  @RequestParam("messageId") Long messageId) {
        try {
            ReviewActionEnum action = ReviewActionEnum.valueOf(actionValue);
            String operationName = Optional.ofNullable(action.getType()).map(OperationConstant.getActionOperationMap()::get).orElse(null);

            if (StringUtils.isNotBlank(operationName)) {
                operationLogFeign.saveOperationLog(OperationLogDTO.builder().operationName(operationName).entityId(messageId).entityType(EntityType.MSG_MANAGEMENT_RFI_REPLY.getName())
                        .appName(ApplicationCode.ADMIN_CONSOLE.name()).orgId(0L).userId(userVO.getUserId()).operationDate(new Date()).operationSource(request.getRequestURL().toString())
                        .operationDesc("rfi-message审核, Message ID:" + messageId).build());
            }
        } catch (Exception ex) {
            log.error("Error when save operation log {}", ex);
        }

        ReviewMessageDTO dto=new ReviewMessageDTO();
        dto.setActionValue(actionValue);
        dto.setMessageId(messageId);
        dto.setReviewerUserName(userVO.getFirstName()+" "+userVO.getLastName());
        dto.setReviewerId(userVO.getUserId());
        dto.setReviewerEmail(userVO.getEmail());
        return messageReviewFeign.review(dto);
    }

    private UserInfoVO getBuyerInfo(Long userId){
        UserVO userResult=ResultUtil.getData(userQueryFeign.getUserByUserId(userId));
        if (Objects.isNull(userResult)) {
            return null;
        }
        UserInfoVO userInfo= OrikaMapperUtil.coverObject(userResult,UserInfoVO.class);
        userInfo.setCountryName(getCountryName(userInfo.getCountryCode()));
        return userInfo;
    }

    private SupplierInfoVO getSupplierInfo(Long supplierId,Long userId) {
        SupplierCommonInfoDTO supplier = ResultUtil.getData(supplierAggFeign.getCommonInfo(supplierId));
        UserVO user=ResultUtil.getData(userQueryFeign.getUserByUserId(userId));
        if (Objects.isNull(supplier) || Objects.isNull(user)) {
            return null;
        }
            return SupplierInfoVO.builder()
                    .supplierId(supplier.getSupplierId())
                    .companyName(supplier.getCompanyDisplayName())
                    .maxContractLevel(supplier.getMaxContractLevel())
                    .verifiedSupplierFlag(supplier.getVerifiedSupplierFlag())
                    .verifiedManufacturerFlag(supplier.getVerifiedManufacturerFlag())
                    .o2oFlag(supplier.getO2oFlag())
                    .memberSince(supplier.getMemberSince())
                    .supplierHomepageUrl(supplier.getSupplierHomepageUrl())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .email(user.getEmail())
                    .userId(userId)
                    .photo(user.getPhoto())
                    .build();
    }

    public String getCountryName(@NonNull String countryCode) {
        log.info("::::dict query param country code is : {}", countryCode);
        if (StringUtils.isNotBlank(countryCode)) {
            Result<QueryDictListResultVO> result = null;
            try {
                QueryDictListRequestVO param=new QueryDictListRequestVO();
                param.setLanguage(EN);
                param.setGroupNameArr(Arrays.asList(COUNTRY));
                result = commonAggFeign.queryDictList(param);
            } catch (Exception e) {
                log.error("::::transfer dict service feign client for get country name error, error message is : {}", e.getMessage());
            }
            if (Objects.nonNull(result)) {
                QueryDictListResultVO dictResultDTO = ResultUtil.getData(result);
                if (Objects.nonNull(dictResultDTO)) {
                    String i18nValue = getI18nValue(COUNTRY, countryCode, dictResultDTO);
                    log.info("::::transfer dict service success, country name is : {}", i18nValue);
                    return i18nValue;
                }
            }
        }
        return CoreConstants.EMPTY_STRING;
    }
    public String getI18nValue(@NonNull String dictCode, @NonNull String i18nKey, @NonNull QueryDictListResultVO dictResultDTO) {
        return Option.of(dictResultDTO.getDictMap()).getOrElse(HashMap::new).getOrDefault(dictCode, Collections.emptyList()).parallelStream().filter(e -> StringUtils.equalsAnyIgnoreCase(e.getI18nKey(), i18nKey)).findAny().map(DictionaryItemVO::getI18nValue).orElse(CoreConstants.EMPTY_STRING);
    }
}
