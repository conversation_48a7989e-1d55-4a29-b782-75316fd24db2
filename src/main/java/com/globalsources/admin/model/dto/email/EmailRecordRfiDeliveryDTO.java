package com.globalsources.admin.model.dto.email;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRecordRfiDeliveryDTO implements Serializable {

    private String emailAddr;

    private Integer periodType;

    private Date startTime;

    private Date endTime;

    private Long pageNum;

    private Long pageSize;
}
