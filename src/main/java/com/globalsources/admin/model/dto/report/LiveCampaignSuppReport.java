package com.globalsources.admin.model.dto.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveCampaignSuppReport {
    private String id;
    private Long supplierId;
    private String supplierName;
    private Long cardCount;
    private Long followCount;
    private Long chatCount;
    private Long submitRfiCount;
    private Long postQcRfiCount;
    private Long offlineScanCount;
    private Long onlineScanCount;

    private Date createDate;
}
