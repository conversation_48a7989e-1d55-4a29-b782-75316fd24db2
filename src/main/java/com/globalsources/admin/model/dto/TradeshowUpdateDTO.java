package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
@ApiModel(value="TradeshowUpdate对象")
public class TradeshowUpdateDTO {

    @NotNull(message = "tradeshowId cannot be null")
    @ApiModelProperty(required = true, value = "展会ID")
    private Long tradeshowId;

    @ApiModelProperty(value = "展会图片链接")
    private String tsImageUrl;

    @ApiModelProperty(value = "展会图片链接Key")
    private String tsImageUrlKey;

    @ApiModelProperty(value = "展会链接")
    private String tsUrl;

    @ApiModelProperty(value = "展会报告链接")
    private String tsReportUrl;

    @ApiModelProperty(value = "是否在APP显示")
    private String displayAppFlag;

}
