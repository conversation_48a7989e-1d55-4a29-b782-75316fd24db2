package com.globalsources.admin.model.vo.helpcenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value="OnlineTopic下的文章元素出参对象 标题下的文章")
public class BffChildrenOnlineContentVO implements Serializable {

    private static final long serialVersionUID = 7145327505204056706L;

    @ApiModelProperty(value = "文章 id")
    private Integer id;

    @ApiModelProperty(value = "文章名称")
    private String name;

    @ApiModelProperty(value = "封面图地址")
    private String imageUrl;

    @ApiModelProperty(value = "链接地址")
    private String webUrl;
}
