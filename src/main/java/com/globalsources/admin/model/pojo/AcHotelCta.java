package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Tradeshow hotels CTA url
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ac_hotel_cta")
public class AcHotelCta extends Model<AcHotelCta> {

    private static final long serialVersionUID = 1L;

    /**
     * CTA id
     */
    @TableId(value = "hotel_cta_id", type = IdType.AUTO)
    private Integer hotelCtaId;

    /**
     * hotel id
     */
    @TableField("hotel_id")
    private Integer hotelId;

    /**
     * phase:1,2,3
     */
    @TableField("phase")
    private Integer phase;

    /**
     * CTA:1,2
     */
    @TableField("cta")
    private Integer cta;

    /**
     * CTA URL
     */
    @TableField("cta_url")
    private String ctaUrl;

    /**
     * Early Bird CTA URL
     */
    @TableField("early_bird_cta_url")
    private String earlyBirdCtaUrl;

    @TableField("create_date")
    private Date createDate;

    @TableField("l_upd_date")
    private Date lUpdDate;

    /**
     * Early Bird CTA:1,2
     */
    @TableField("early_bird_cta")
    private Integer earlyBirdCta;


    @Override
    protected Serializable pkVal() {
        return this.hotelCtaId;
    }

}
