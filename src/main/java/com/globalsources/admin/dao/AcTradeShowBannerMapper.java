package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.admin.model.pojo.AcTradeShowBannerPO;
import com.globalsources.admin.model.vo.TradeShowBannerVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
public interface AcTradeShowBannerMapper extends BaseMapper<AcTradeShowBannerPO> {

    /**
     * online supplier booth
     * @param supplierId
     * @param date
     */
    TradeShowBannerVO getTradeShowBannerByKey(@Param("supplierId") Long supplierId, @Param("date") Date date);

}
