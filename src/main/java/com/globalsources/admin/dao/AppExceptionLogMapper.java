package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.admin.model.pojo.AppExceptionLogPO;
import com.globalsources.admin.model.vo.app.AppExceptionUrlCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppExceptionLogMapper extends BaseMapper<AppExceptionLogPO> {
    List<AppExceptionUrlCountVO> queryAppExceptionUrlCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("businessType") String businessType);
}
