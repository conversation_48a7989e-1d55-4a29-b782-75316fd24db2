package com.globalsources.data.exchange.job;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.globalsources.data.exchange.constans.CommonConstant;
import com.globalsources.data.exchange.dao.OnlineProductDao;
import com.globalsources.data.exchange.dao.SupplierInfoDao;
import com.globalsources.data.exchange.dao.es.ProductElasticsearchRepositories;
import com.globalsources.data.exchange.model.dto.ProductInfoDTO;
import com.globalsources.data.exchange.model.dto.ProductQueryDTO;
import com.globalsources.data.exchange.model.es.ProductIndex;
import com.globalsources.data.exchange.service.ProductIndexService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/11/08
 */
@Slf4j
@Component
public class UpdateSupplierSectionHandler {

    @Autowired
    private SupplierInfoDao supplierInfoDao;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final Long LIMIT_NUM = 200L;

    private Map<Long,String> fullCategoryNameCacheMap=new HashMap<>(15000);

    @Autowired
    private ProductElasticsearchRepositories repositories;

    @Autowired
    private OnlineProductDao onlineProductDao;

    @Autowired
    private ProductIndexService productIndexService;

    @XxlJob(value = "updateSupplierSectionHandler")
    public ReturnT<String> updateSupplierSectionHandler() {
        try {
            log.info("----------begin updateSupplierSectionHandler");
            //N 小时
            String param =  XxlJobHelper.getJobParam();

            ProductQueryDTO supplierQueryDTO = new ProductQueryDTO();
            supplierQueryDTO.setStartDate(new DateTime().minusHours(Integer.parseInt(param)).toDate());
            supplierQueryDTO.setEndDate(new Date());
            supplierQueryDTO.setLimit(LIMIT_NUM);
            supplierQueryDTO.setLastSupplierId(null);

            List<Long> supplierList = supplierInfoDao.selectSupplierId(supplierQueryDTO);
            List<Long> filterSupplierList;
            List<ProductIndex> productList;
            ProductInfoDTO productQueryDTO = new ProductInfoDTO();
            productQueryDTO.setLimit(LIMIT_NUM.intValue());

            while (CollectionUtils.isNotEmpty(supplierList)) {
                log.info("updateSupplierSectionHandler supplierList size:{}", supplierList.size());
                filterSupplierList = filterNoChangedSupplier(supplierList);

                for(Long supplierId :filterSupplierList){
                    productQueryDTO.setProductId(null);
                    productQueryDTO.setSupplierId(supplierId);
                    productList = onlineProductDao.getProductIndexListBySupplier(productQueryDTO);
                    while (CollectionUtils.isNotEmpty(productList)) {
                        log.info("updateSupplierSectionHandler supplier : {} productList size : {}", supplierId, productList.size());
                            for (ProductIndex index: productList) {
                                if (StringUtils.isNotEmpty(index.getBusinessTypeStr())){
                                    index.setBusinessType(CollUtil.newArrayList(StringUtils.split(index.getBusinessTypeStr(), ",")));
                                }
                                setCategoryName(index);
                            }
                        repositories.saveAll(productList);
                        productQueryDTO.setProductId(getLastProductId(productList));
                        productList = onlineProductDao.getProductIndexListBySupplier(productQueryDTO);
                    }
                }
                supplierQueryDTO.setLastSupplierId(getLastSupplierId(supplierList));
                supplierList = supplierInfoDao.selectSupplierId(supplierQueryDTO);
            }
            log.info("updateSupplierSectionHandler  all done");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("updateSupplierSectionHandler exception, e:{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }

    private Long getLastSupplierId(List<Long> supplierList) {
        return supplierList.get(supplierList.size()-1);
    }

    private Long getLastProductId(List<ProductIndex> list){
        return list.get(list.size()-1).getProductId();
    }

    private void setCategoryName(ProductIndex index) {
        try {
            if(fullCategoryNameCacheMap.containsKey(index.getCategoryL4())){
                index.setCategoryName(fullCategoryNameCacheMap.get(index.getCategoryL4()));
                return;
            }
            StringBuilder categoryName= new StringBuilder();
            categoryName.append(redisTemplate.opsForValue().get(CommonConstant.RFX_CATEGORY_NAME_CACHE+index.getCategoryL1())).append("/");
            categoryName.append(redisTemplate.opsForValue().get(CommonConstant.RFX_CATEGORY_NAME_CACHE+index.getCategoryL2())).append("/");

            categoryName.append(redisTemplate.opsForValue().get(CommonConstant.RFX_CATEGORY_NAME_CACHE+index.getCategoryL3())).append("/");

            categoryName.append(redisTemplate.opsForValue().get(CommonConstant.RFX_CATEGORY_NAME_CACHE+index.getCategoryL4()));
            index.setCategoryName(categoryName.toString());
            fullCategoryNameCacheMap.put(index.getCategoryL4(), categoryName.toString());
        } catch (Exception e) {
            log.warn("ProductIndexEsSyncJob setCategoryName error:{}", JSON.toJSONString(e));
        }
    }

    private List<Long> filterNoChangedSupplier(List<Long> supplierIdList){
        log.info("updateSupplierSectionHandler filterNoChangedSupplier supplierIdList before : {} ",supplierIdList);
        List<Long> result = new ArrayList<>(supplierIdList.size());
        ProductInfoDTO requestDTO = new ProductInfoDTO();
        requestDTO.setLimit(1);

        List<ProductIndex> productList;
        ProductIndex dbData;
        ProductIndex esData;

        for(Long supplierId : supplierIdList){
            requestDTO.setSupplierId(supplierId);
            productList = onlineProductDao.getProductIndexListBySupplier(requestDTO);
            esData = productIndexService.getESProductBySupplier(supplierId);

            if(CollectionUtils.isEmpty(productList) || Objects.isNull(productList.get(0)) || Objects.isNull(esData)){
                log.warn("updateSupplierSectionHandler filterNoChangedSupplier has no product supplier : {} ",supplierId);
                continue;
            }
            dbData = productList.get(0);

            if(Objects.equals(dbData.getContractCode(),esData.getContractCode())
                    && Objects.equals(dbData.getSupplierName(),esData.getSupplierName())
                    && Objects.equals(dbData.getSupplierContentScore(),esData.getSupplierContentScore())
                    && Objects.equals(dbData.getSupplierMainCategoryId(),esData.getSupplierMainCategoryId())
                    && Objects.equals(dbData.getOemFlag(),esData.getOemFlag())
                    && Objects.equals(dbData.getVerifiedManufacturerFlag(),esData.getVerifiedManufacturerFlag())
                    && Objects.equals(dbData.getVrFlag(),esData.getVrFlag())
                    && Objects.equals(dbData.getBusinessTypeStr(),esData.getBusinessTypeStr())){
                log.info("updateSupplierSectionHandler filterNoChangedSupplier supplier has no change : {},dbData:{},esData:{} ",supplierId,dbData,esData);
            }else{
                log.info("updateSupplierSectionHandler filterNoChangedSupplier supplier changed : {},dbData:{},esData:{} ",supplierId,dbData,esData);
                result.add(supplierId);
            }
        }
        log.info("updateSupplierSectionHandler filterNoChangedSupplier supplierIdList after : {} ",result);

        return result;
    }
}
