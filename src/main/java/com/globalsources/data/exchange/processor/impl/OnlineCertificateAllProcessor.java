package com.globalsources.data.exchange.processor.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.data.exchange.constans.Operation;
import com.globalsources.data.exchange.dao.OnlineProductDao;
import com.globalsources.data.exchange.model.event.OnlineCertificateEvent;
import com.globalsources.data.exchange.processor.OnlineCertificateProcessor;
import com.globalsources.data.exchange.service.ProductCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 处理供应商认证变更
 */
@Component
@Slf4j
public class OnlineCertificateAllProcessor implements OnlineCertificateProcessor {

    @Autowired
    private ProductCommonService productCommonService;

    @Autowired
    private OnlineProductDao onlineProductDao;
    @Override
    public Object process(OnlineCertificateEvent before, OnlineCertificateEvent after, OnlineCertificateEvent alwaysPass, String op) {
        try {
            switch (op) {
                case Operation.C:
                case Operation.D:
                    productCommonService.updateProductCertificate(Collections.emptyList(),after.getSupplierId());
                    break;
                case Operation.U:
                    productCommonService.updateProductCertificate(Collections.emptyList(),alwaysPass.getSupplierId());
                    break;
                default:
                    log.error("Unsupported operation: {}, before: {}, after: {}", op, before, after);
            }
        } catch (Exception e) {
            log.error("Process OnlineCertificateEvent error,before:{},after:{},alwaysPass:{}, error: {}", before,after,alwaysPass,JSON.toJSONString(e));
        }
        return null;
    }
}
