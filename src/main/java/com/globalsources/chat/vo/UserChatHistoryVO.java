package com.globalsources.chat.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigInteger;
import java.util.Date;

@Data
public class UserChatHistoryVO {
    @ApiModelProperty(value = "ID of Entity")
    private String entityId;

    @ApiModelProperty(value = "The session id of the chat, which is the concatenation of the sender ldap user id and recipient ldap user id")
    private BigInteger sessionId;

    @ApiModelProperty(value = "The ldap user id of the message sender")
    private Long senderLdapUserId;

    @ApiModelProperty(value = "The ldap user id of the message recipient")
    private Long recipientLdapUserId;

    @ApiModelProperty(value = "The message sequence number")
    private Long seqNum;

    @ApiModelProperty(value = "The type of the message")
    private String chatContentType;

    @ApiModelProperty(value = "The message MSG_CONTENT")
    private String chatContent;

    @ApiModelProperty(value = "The sender type of the message. B stands for buyer and S stands for supplier")
    private String senderType;

    @ApiModelProperty(value = "The supplier id, e.g. 2008829670481")
    private Long supplierId;

    @ApiModelProperty(value = "Application source where message is sent from.")
    private String sourceAppCode;

    @ApiModelProperty(value = "Entity type (e.g. RFI/RFQ/Product)")
    private String entityType;

    private Date createDate;

    @ApiModelProperty(value = "The create timestamp of the message")
    private Date refreshDate;
}
