/**
 * <a>Title: EmailSubjectEnum </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/9-16:24
 */
package com.globalsources.email.enums;

public enum EmailSubjectEnum {
    //买家注册效验
    BUYER_REGISTER_VERIFY("BUYER_REGISTER_VERIFY", "Welcome to Global Sources! This is how to get started"),

    USER_DOI_CODE_V2("USER_DOI_CODE_V2","Global Sources - Activate your account"),

    SUCCESS_REGISTER("SUCCESS_REGISTER", "Welcome to Global Sources! This is how to get started"),
    SUCCESS_REGISTER_DOI("SUCCESS_REGISTER_DOI", "Welcome to Global Sources! This is how to get started"),
    //RFI询盘供应商通知{0}公司名称
    SUPPLIER_INQUIRY_RFI_NOTICE("SUPPLIER_INQUIRY_RFI_NOTICE", "Global Sources Buyer Inquiry： {0}"),
    //RFI供应商回复通知 {0}姓名
    SUPPLIER_INQUIRY_RFI_REPLY("SUPPLIER_INQUIRY_RFI_REPLY", "Message Replied：{0} has replied you"),
    //供应商RFQ通知 {0}姓名，{1}商品名
    SUPPLIER_INQUIRY_RFQ_NOTICE("SUPPLIER_INQUIRY_RFQ_NOTICE", "You have a new RFQ from {0}, please quote now!"),
    //RFQ重新分配模板
    SUPPLIER_RFQ_REDISTRIBUTE("SUPPLIER_RFQ_REDISTRIBUTE", "An RFQ has been reassigned to you"),
    //买家RFI回复通知 {0}姓名
    BUYER_INQUIRY_RFI_REPLY("BUYER_INQUIRY_RFI_REPLY", "Global Sources Inquiry Re：{0} replied you"),
    //买家RFQ通知 {0}公司名
    BUYER_INQUIRY_RFQ_NOTICE("BUYER_INQUIRY_RFQ_NOTICE", "A new quotation received {0}"),
    //RFI[供应商询盘]通知 {0}公司名称
    SUP_INQUIRY_RFI_NOTICE("SUP_INQUIRY_RFI_NOTICE", "环球资源新询盘：来自 {0}"),
    //询盘重新分配
    SUPPLIER_RFI_REDISTRIBUTE("SUPPLIER_RFI_REDISTRIBUTE", "An inquiry has been reassigned to you"),
    //买家chat通知
    BUYER_CHAT_NOTICE("BUYER_CHAT_NOTICE", "A new chat message received"),
    //卖家chat通知
    SUPPLIER_CHAT_NOTICE("SUPPLIER_CHAT_NOTICE", "收到一条聊天消息"),
    //订阅PA
    SUBSCRIBE_PA("SUBSCRIBE_PA", ""),
    SUBSCRIBE_PA_ESES("SUBSCRIBE_PA_ESES", ""),
    SUBSCRIBE_PA_DEDE("SUBSCRIBE_PA_DEDE", ""),
    SUBSCRIBE_PA_FRFR("SUBSCRIBE_PA_FRFR", ""),
    SUBSCRIBE_PA_PTPT("SUBSCRIBE_PA_PTPT", ""),
    //订阅SA
    SUPPLIER_ALERT("SUPPLIER_ALERT","Updates from your followed suppliers"),
    //upsell通知
    SUPPLIER_INQUIRY_UPSELL_NOTICE("SUPPLIER_INQUIRY_UPSELL_NOTICE", "Global Sources Buyer Inquiry： {0}"),
    //账号申诉
    ACCOUNT_APPEAL("ACCOUNT_APPEAL","Appeal To Reactive Disabled Account"),
    //默认空模板
    DEFAULT_EMPTY("DEFAULT_EMPTY","default_empty.html"),
    //礼品兑换成功通知 - to 兑换用户
    REWARDS_NEW_ORDER("REWARDS_NEW_ORDER", "A new {0} Rewards Redemption order has been received."),
    //礼品兑换成功通知 - to 礼品发放人员
    REWARDS_RECEIVED("REWARDS_RECEIVED", "Global Sources Sourcing Club has received your gift redemption request"),
    EMATCH_BUYER("EMATCH_BUYER", "Global Sources has found some suppliers for you"),
    EMATCH_TEAM("EMATCH_TEAM", "Buyer {0} made an appointment for a video meeting with suppliers"),
    //电子杂志
    EMAGAZINE_ALERT("EMAGAZINE_ALERT","Reminder: Download Your {0} E-magazines"),
    ADMIN_CONSOLE_SEO_REPORT("ADMIN_CONSOLE_SEO_REPORT","Client SEO Audit Data"),

    //公司图片审核 图片批量上传处理结果
    SC_ADMIN_REVIEW_ORG_IMG_PACK_UPLOAD_RESULT("SC_ADMIN_REVIEW_ORG_IMG_PACK_UPLOAD_RESULT","Admin Console - Image Upload Report"),
    //公司视频审核 reject 通知邮件
    SC_ADMIN_VIDEO_REJECT("SC_ADMIN_VIDEO_REJECT","Your Posting Request cannot be Processed"),
    //产品审核 reject 通知邮件
    SC_ADMIN_PRODUCT_REJECT("SC_ADMIN_PRODUCT_REJECT","Your Posting Request cannot be Processed"),
    //公司section信息审核被拒通知
    SECTION_REVIEW_REJECT_NOTICE("SECTION_REVIEW_REJECT_NOTICE","Your Posting Request cannot be Processed"),
    //认证信息审核 reject 通知邮件
    SC_ADMIN_CERTIFICATE_REJECT("SC_ADMIN_CERTIFICATE_REJECT","Your Posting Request cannot be Processed"),
    //PP Attribute Report
    SC_ADMIN_PP_ATTRIBUTE_REPORT("SC_ADMIN_PP_ATTRIBUTE_REPORT", "Admin Console - PP Attribute Report"),
    //供应商合同rollover 产品视频下线通知
    SUPP_ROLLOVER_PROD_VIDEO_OFFLINE("SUPP_ROLLOVER_PROD_VIDEO_OFFLINE", "Contract Roll Over - Product Video Unpost Notification"),
    //供应商合同rollover下线通知
    SUPP_ROLLOVER_UNPOST_NOTICE("SUPP_ROLLOVER_UNPOST_NOTICE", "Contract Roll Over Unpost Notification"),
    //产品认证过期前10天 通知邮件
    SC_PRODUCT_CERTIFICATE_EXPIRING_NOTICE("SC_PRODUCT_CERTIFICATE_EXPIRING_NOTICE", "Your certificate will expire soon"),
    //公司认证过期前7天 通知邮件
    SC_SECTION_CERTIFICATE_EXPIRING_NOTICE("SC_SECTION_CERTIFICATE_EXPIRING_NOTICE", "Your certification(s) will soon expire"),
    //公司上线通知
    COMPANY_ONLINE_NOTICE("COMPANY_ONLINE_NOTICE", "Your Global Sources Online Homepage is online - {0}"),
    //公司合同即将到期通知
    SUPP_CONTRACT_EXPIRY_NOTICE("SUPP_CONTRACT_EXPIRY_NOTICE", "{0} - Contract will be expired notification"),
    //产品rollover下线通知
    PRODUCT_ROLLOVER_UNPOST_NOTICE("PRODUCT_ROLLOVER_UNPOST_NOTICE", "Contract Roll Over Unpost Notification"),
    //客服服务报告
    CLIENT_SERVICE_REPORT_EN("CLIENT_SERVICE_REPORT_EN", "Global Sources Client Service Report - {0}"),
    CLIENT_SERVICE_REPORT_ZH("CLIENT_SERVICE_REPORT_ZH", "环球资源客户服务报告 - {0}"),
    CLIENT_SERVICE_REPORT_TW("CLIENT_SERVICE_REPORT_TW", "環球資源客戶服務報告 - {0}"),
    //申请添加第三方展会通知
    ADD_NON_GS_TRADE_SHOW_NOTICE("ADD_NON_GS_TRADE_SHOW_NOTICE", "Tradeshow Creation Alert - {0}"),

    //供应商指标测评结果通知
    SUPP_EVALUATE_RESULT_NOTICE("SUPP_EVALUATE_RESULT_NOTICE", "MC Incentive Program KPI Passed - {0}"),

    //展会Guide变更申请及撤销变更申请
    TS_GUIDE_CHANGE_EN("TS_GUIDE_CHANGE_EN", "The application to modify the exhibition information ({0})"),
    TS_GUIDE_CHANGE_ZH("TS_GUIDE_CHANGE_ZH", "参展信息修改申请，请及时处理（{0}）"),
    TS_GUIDE_UNDO_EN("TS_GUIDE_UNDO_EN", "The application to modify the exhibition information has been canceled ({0})"),
    TS_GUIDE_UNDO_ZH("TS_GUIDE_UNDO_ZH", "参展信息修改申请已撤销（{0}）"),

    TS_GUIDE_CHANGE_CONFIRM_EN("TS_GUIDE_CHANGE_CONFIRM_EN", "Exhibition information has been modified, please confirm ({0})"),
    TS_GUIDE_CHANGE_CONFIRM_ZH("TS_GUIDE_CHANGE_CONFIRM_ZH", "参展信息已修改，请确认（{0}）"),
    TS_GUIDE_TO_BE_CONFIRM_EN("TS_GUIDE_TO_BE_CONFIRM_EN", "Confirmation of exhibitor information ({0})"),
    TS_GUIDE_TO_BE_CONFIRM_ZH("TS_GUIDE_TO_BE_CONFIRM_ZH", "参展信息确认（{0}）"),

    TS_BADGE_APPLY_NOTICE_EN("TS_BADGE_APPLY_NOTICE_EN", "Exhibitor Badge Application ({0})"),
    TS_BADGE_APPLY_NOTICE_ZH("TS_BADGE_APPLY_NOTICE_ZH", "参展商胸卡申请（{0}）"),

    TS_REGISTER_REMINDER_EN("TS_REGISTER_REMINDER_EN", "Supplier Center Access Notification – {0}"),
    TS_REGISTER_REMINDER_ZH("TS_REGISTER_REMINDER_ZH", "展商后台开通提醒 - {0}"),
    TS_REGISTER_REMINDER_TW("TS_REGISTER_REMINDER_TW", "賣家中心開通提醒 – {0}"),

    TS_SERVICE_OPEN_NOTICE_EN("TS_SERVICE_OPEN_NOTICE_EN", "{1} {0} {2} Notice on Exhibitor Digital Services - {3}"),
    TS_SERVICE_OPEN_NOTICE_ZH("TS_SERVICE_OPEN_NOTICE_ZH", "{0}年{1}月{2}展商数字化服务必知 - {3}"),
    TS_SERVICE_OPEN_NOTICE_TW("TS_SERVICE_OPEN_NOTICE_TW", "{0}年{1}月{2}展商數位化服務必知 - {3}"),

    //销售线索通知信 - 现刊客户有兴趣加买展会
    SC_RATE_AND_FLOOR_PLAN("SC_RATE_AND_FLOOR_PLAN","现刊客户有兴趣加买展会 - {0}"),
    /************************************ supplier center user ************************************************/
    //激活用户权限（添加直接通过或者审核通过）
    SC_USER_ROLE_ADD("SC_USER_ROLE_ADD", "Supplier Center - Account Activation Notification"),
    //分配用户权限通知
    SC_USER_ROLE_ASSIGNMENT_UPDATE("SC_USER_ROLE_ASSIGNMENT_UPDATE", "Supplier Center - User Rights Update Notification"),
    //子账号修改邮箱通知主账号
    SC_USER_ROLE_CHANGE_EMAIL_NOTICE_TO_MASTER("SC_USER_ROLE_CHANGE_EMAIL_NOTICE_TO_MASTER", "Supplier Center - User changed Global Sources account email address"),
    //子账号修改邮箱，没有主账号，通知cso
    SC_USER_ROLE_CHANGE_EMAIL_TO_CSO("SC_USER_ROLE_CHANGE_EMAIL_TO_CSO", "Supplier Center - Sub Account changed Global Sources account email address"),
    //主账号修改邮箱，通知cso
    SC_USER_ROLE_CHANGE_MASTER_EMAIL("SC_USER_ROLE_CHANGE_MASTER_EMAIL", "Supplier Center - Master Account changed Global Sources account email address"),
    //内部账户修改邮箱，通知cso
    SC_USER_ROLE_CHANGE_INTERNAL_EMAIL("SC_USER_ROLE_CHANGE_INTERNAL_EMAIL", "Supplier Center - Internal Account changed Global Sources account email address"),
    //删除用户通知
    SC_USER_ROLE_DELETE("SC_USER_ROLE_DELETE", "Supplier Center - Access cancellation"),
    //用户权限申请待审批通知
    SC_USER_ROLE_REQUEST("SC_USER_ROLE_REQUEST", "Supplier Center - New User request"),
    //用户权限申请，批准通知
    SC_USER_ROLE_REQUEST_APPROVE("SC_USER_ROLE_REQUEST_APPROVE", "Supplier Center - Approved to your new user request"),
    //用户权限申请，拒绝通知
    SC_USER_ROLE_REQUEST_REJECT("SC_USER_ROLE_REQUEST_REJECT", "Supplier Center - Rejected to your new user request"),
    //转让主账号，新主账号通知
    SC_USER_ROLE_TRANSFER_MASTER_FOR_NEW("SC_USER_ROLE_TRANSFER_MASTER_FOR_NEW", "Supplier Center - Master account Updated Notification"),
    //转让主账号，旧主账号通知
    SC_USER_ROLE_TRANSFER_MASTER_FOR_OLD("SC_USER_ROLE_TRANSFER_MASTER_FOR_OLD", "Supplier Center - Master Account Updated Notification"),
    SC_USER_ROLE_CHANGE("SC_USER_ROLE_CHANGE", "Supplier Center - User Role Update Notification"),

    //直播活动开始通知
    LIVESTREAM_START_NOTIFY("LIVESTREAM_START_NOTIFY","The livestreaming event you subscribe to will start in 5 minutes"),

    RFI_AUTO_DIST_NOTICE_SUBJECT_EN("RFI_AUTO_DIST_NOTICE_SUBJECT_EN","Global Source Inquiry Distribution Summary Report - {0}"),
    RFI_AUTO_DIST_NOTICE_SUBJECT_ZH("RFI_AUTO_DIST_NOTICE_SUBJECT_ZH","环球资源询盘自动分配汇总报告 – {0}"),
    RFI_AUTO_DIST_NOTICE_SUBJECT_TW("RFI_AUTO_DIST_NOTICE_SUBJECT_TW","環球資源詢盤自動分配匯總報告 – {0}"),

    //rfi 潜在商机询盘汇总通知邮件
    RFI_SUPPLIER_POTENTIAL_OPPORTUNITY("RFI_SUPPLIER_POTENTIAL_OPPORTUNITY", "环球资源潜在商机询盘汇总报告 – {0}"),

    PRODUCT_ISSUE_LOG_SUBJECT_NEW("PRODUCT_ISSUE_LOG_SUBJECT_NEW", "Notifications of needing modification"),
    PRODUCT_ISSUE_LOG_SUBJECT_UNPOST("PRODUCT_ISSUE_LOG_SUBJECT_UNPOST", "Notification of pulling down"),
    PRODUCT_ISSUE_LOG_SUBJECT_DELETE("PRODUCT_ISSUE_LOG_SUBJECT_DELETE", "Notification of deleting"),

    SUPPLIER_PENALTY_LOG_NOTICE("SUPPLIER_PENALTY_LOG_NOTICE", "Notifications of Penalty"),
    SUPPLIER_VIOLATION_NOTICE("SUPPLIER_VIOLATION_NOTICE", "Notifications of Violation"),

    /**
     * 供应商违规处罚结束提醒邮件标题
     */
    SUPPLIER_PENALTY_END_REMINDER("SUPPLIER_PENALTY_END_REMINDER", "Notifications of Penalty"),


    /**
     * AC审核结果通知
     */
    PROMOTION_REQUEST_AUDIT_APPROVED("PROMOTION_REQUEST_AUDIT_APPROVED","Your submission for Analyst's Choice has been approved"),
    PROMOTION_REQUEST_AUDIT_REJECTED("PROMOTION_REQUEST_AUDIT_REJECTED","Your submission for Analyst's Choice has been rejected"),



    SYP_TRADE_SHOW("SYP_TRADE_SHOW", "客户有兴趣加买展会 – {0}"),
    SYP_TRADE_INTELLIGENCE("SYP_TRADE_INTELLIGENCE", "GS服务查询：客户查询已核实供应商服务"),


    INVITATION_DAILY_REPORT("INVITATION_DAILY_REPORT", "Buyer Invitation Daily Report"),

    // 广告位自动续订成功通知
    BOOKING_AUTO_BIDDING_SUCCESS_SUBJECT("BOOKING_AUTO_BIDDING_SUCCESS_SUBJECT", "Auto-Bidding for keyword {0} of {1} was completed"),
    // 广告位自动续订失败通知
    BOOKING_AUTO_BIDDING_FAILED_SUBJECT("BOOKING_AUTO_BIDDING_FAILED_SUBJECT", "Auto-bidding for keyword {0} of {1} cannot be completed"),
    TS_REG_CSO_DAILY_REPORT("TS_REG_CSO_DAILY_REPORT", "TS REG CSO Daily Report"),
    TS_REG_AE_DAILY_REPORT("TS_REG_AE_DAILY_REPORT", "TS REG AE Daily Report"),
    TS_INDONESIAN_PRESS_INQUIRY("TS_INDONESIAN_PRESS_INQUIRY", "TS INDONESIAN PRESS INQUIRY"),

    SYP_SUPP_ACTIVATE_YOUR_ACCOUNT_EN("SYP_SUPP_ACTIVATE_YOUR_ACCOUNT_EN", "Global Sources: Activate your account"),
    SYP_SUPP_ACTIVATE_YOUR_ACCOUNT_ZH("SYP_SUPP_ACTIVATE_YOUR_ACCOUNT_ZH", "环球资源：激活您的账户"),
    SC_LOG_IN_AND_POST_PROD_IN_SC("SC_LOG_IN_AND_POST_PROD_IN_SC", "Global Sources: Log in and post your products in Supplier Center"),



    // 新PA邮件
    HOT_PRODUCT_ALERT("HOT_PRODUCT_ALERT", "This Week’s Hot & Trending Products for You"),
    HOT_PRODUCT_ALERT_ESES("HOT_PRODUCT_ALERT_ESES", "Los productos más populares y de tendencia de esta semana para ti"),
    HOT_PRODUCT_ALERT_DEDE("HOT_PRODUCT_ALERT_DEDE", "Diese Woche heiße Produkte und Trends für Sie"),
    HOT_PRODUCT_ALERT_FRFR("HOT_PRODUCT_ALERT_FRFR", "Les produits phares et tendances de cette semaine pour vous"),
    HOT_PRODUCT_ALERT_PTPT("HOT_PRODUCT_ALERT_PTPT", "Os produtos em alta e tendências desta semana para você"),

    // 首席问鼎广告位续订通知邮件
    PRIMARY_TOP_BOOKING_RENEWAL_NOTIFICATION("PRIMARY_TOP_BOOKING_RENEWAL_NOTIFICATION", "Your Primary and Top Row Ad will expire soon, renew it now!"),

    // 搜索栏广告位续订通知邮件
    KSB_RENEWAL_NOTIFICATION("KSB_RENEWAL_NOTIFICATION", "Your Keyword Search Box Ad will expire soon, renew it now!"),

    // 标志广告位续订通知邮件
    BANNER_BOOKING_RENEWAL_NOTIFICATION("BANNER_BOOKING_RENEWAL_NOTIFICATION", "Your Banner Ad will expire soon, renew it now!"),

    CRM_SUPPLIER_IMPORT_REQUEST("CRM_SUPPLIER_IMPORT_REQUEST","Supplier Center Import Complete"),

    VIRUS_SCAN_RESULT_PRODUCT_NOTIFICATION("VIRUS_SCAN_RESULT_PRODUCT_NOTIFICATION","Suspicious File Deletion Notification")

    ;

    private String key;

    private String value;

    EmailSubjectEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String match(String name) {
        EmailSubjectEnum[] values = EmailSubjectEnum.values();
        for (EmailSubjectEnum emailEnum : values) {
            if (emailEnum.key.equals(name)) {
                return emailEnum.value;
            }
        }
        return null;
    }
}
