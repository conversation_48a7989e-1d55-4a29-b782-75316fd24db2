package com.globalsources.email.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/28
 */
public class EmailConstant {

    private EmailConstant() {
    }

    public static final String SMTP_FROM = "mail.smtp.from";
    public static final String RETURN_PATH = "Return-Path";
    public static final String ERRORS_TO = "Errors-to";
    public static final String SENDER = "Sender";
    public static final String REPLY_TO = "Reply-To";
    public static final String BUYER_TITLE = "buyerTitle";
    public static final String X_VIRTUAL_MTA = "X-Virtual-MTA";
    public static final String SENDER_EMAIL = "<EMAIL>";
    public static final String SUPPLIER_SERVICE_PREFIX = "supplierservice@";
    public static final String SUPPLIER_SERVICE = "supplierservice";
    public static final String DEFAULT_FROM_DOMAIN = "globalsources.com";
    public static final String DEFAULT_FROM_EMAIL = "<EMAIL>";
    public static final String PA_BOUNCE_DOI_FROM_EMAIL = "<EMAIL>";
    public static final String PSC_FROM_EMAIL = "<EMAIL>";

    public static final String MESSAGE_ID = "Message-Id";
    public static final String FROM = "From";
    public static final String TO = "To";

    public static final String IMPORT_DIR = "import";
    public static final String BACKUP_DIR = "backup";

    public static final String SPACE_STR = " ";


    public static final String CODE_HK = "HK";

    public static final String CODE_SFTP = "SFTP";

    public static final String ELOQUA_DIR = "Eloqua_Files";


    public static final String ELOQUA_BOUNCE_FILE = "bouncelist-ddmmyyyy.csv";

    public static final String ELOQUA_SYNC = "ELOQUA_SYNC";

    public static final String BUCKET_DEV = "gsol-dev-maillog";

    private static final List<String> deliveryDeleteEmailType = Lists.newArrayList(
            EmailTemplateEnum.SUBSCRIBE_PA.getKey(),
            EmailTemplateEnum.SUPPLIER_ALERT.getKey(),
            EmailTemplateEnum.EMAGAZINE_ALERT.getKey(),
            // 为防止表记录增长太快，HPA 发送成功的无需记录，失败的才记录
            EmailTemplateEnum.HOT_PRODUCT_ALERT.getKey()
    );

    public static List<String> getDeliveryDeleteEmailType() {
        return deliveryDeleteEmailType;
    }

}
