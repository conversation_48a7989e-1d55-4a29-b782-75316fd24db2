/**
 * <a>Title: SupplierOrderVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/6/1-14:18
 */
package com.globalsources.email.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierOrderPO implements Serializable {

    /** 供应商姓名*/
    private String supplierFirstName;

    private String supplierLastName;

    private Long supplierId;

    /** 订单号*/
    private String orderNo;

    /** 买家姓名*/
    private String productName;

    /** 买家姓名*/
    private String buyerFirstName;

    private String buyerLastName;

    private Long buyerId;

    private String message;

    /** 买家公司*/
    private String buyerCompanyName;

    /** 国家地区*/
    private String country;
}
