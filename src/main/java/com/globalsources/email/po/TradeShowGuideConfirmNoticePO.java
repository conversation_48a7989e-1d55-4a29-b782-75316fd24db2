package com.globalsources.email.po;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TradeShowGuideConfirmNoticePO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5486765624798581784L;

	private String orgId;

	private String orgName;

	private String deadlineZh;

	private String deadlineEn;

	private String csoEmail;

	private String csoFirstName;

	private String csoLastName;

	private String language;

	private String boothNum;

	private String tsNameZh;

	private String tsNameEn;

	@ApiModelProperty(value = "Show Date/展览日期: 2024/04/11 - 14")
	private String tsDate;

	@ApiModelProperty(value = "Venue/地点: AsiaWorld-Expo , Hong Kong SAR")
	private String tsAddress;

	@ApiModelProperty(value = "Booth Type/展位类型")
	private String boothTypeEn;

	@ApiModelProperty(value = "Booth Type/展位类型")
	private String boothTypeCn;

	@ApiModelProperty(value = "Number of Boot/展位数量")
	private Integer boothSize;

	@ApiModelProperty(value = "Booth Area/展位面积")
	private Integer boothArea;

	@ApiModelProperty(value = "Product Information/产品信息")
	private String category;

	@ApiModelProperty(value = "Company Name (English)")
	private String supplierName;

	@ApiModelProperty(value = "Company Name in Local Language")
	private String supplierNameLocal;

	@ApiModelProperty(value = "Company Name in Tradictional Chinese")
	private String supplierNameTch;

	@ApiModelProperty(value = "Show Guide中的Fair Contact Person")
	private String tsContactPerson;

	@ApiModelProperty(value = "Show Guide中的Telephone Number")
	private String tsTelephone;

	@ApiModelProperty(value = "Show Guide中的Email Address")
	private String tsEmailAddr;

	@ApiModelProperty(value = "Trade show country code")
	private String tsCountryCode;
}
