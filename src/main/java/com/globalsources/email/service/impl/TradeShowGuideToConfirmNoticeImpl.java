package com.globalsources.email.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.email.enums.EmailSubjectEnum;
import com.globalsources.email.enums.EmailTemplateEnum;
import com.globalsources.email.po.EmailModelPO;
import com.globalsources.email.po.TradeShowGuideConfirmNoticePO;
import com.globalsources.email.service.SendEmailService;
import com.globalsources.email.utils.SendEmailUtils;
import com.globalsources.framework.enums.LanguageDicEnum;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * TradeShowGuideToConfirmNoticeImpl
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Service(value = "TradeShowGuideToConfirmNoticeImpl")
@Slf4j
public class TradeShowGuideToConfirmNoticeImpl implements SendEmailService {

    @Autowired
    private SendEmailUtils sendEmailUtils;

    /**
     * 公司上线通知邮件发送
     */
    @Override
    public void sendEmailHtml(String prefix, EmailModelPO emailModelPO) throws MessagingException, IOException, TemplateException {
        TradeShowGuideConfirmNoticePO noticePO = JSON.parseObject((String) emailModelPO.getTemplateParams()
                .get(EmailTemplateEnum.TS_GUIDE_TO_BE_CONFIRM.getKey()), TradeShowGuideConfirmNoticePO.class);
        Map<String, Object> context = JSON.parseObject(JSON.toJSONString(noticePO), HashMap.class);
        int year = LocalDate.now().getYear();
        context.put("year", String.valueOf(year));
        String subject = EmailSubjectEnum.match(EmailSubjectEnum.TS_GUIDE_TO_BE_CONFIRM_EN.getKey());
        if (LanguageDicEnum.ZH_CN.getValue().equals(noticePO.getLanguage())) {
            subject = EmailSubjectEnum.match(EmailSubjectEnum.TS_GUIDE_TO_BE_CONFIRM_ZH.getKey());
        }
        subject = subject.replace("{0}", noticePO.getBoothNum());
        emailModelPO.setSubject(subject);
        log.info("trade show guide to be confirm notice param={}", emailModelPO.getTemplateParams());
        sendEmailUtils.sendEmailTemplate(prefix, "epsce", "hkaws-port25", emailModelPO, context);
    }
}
