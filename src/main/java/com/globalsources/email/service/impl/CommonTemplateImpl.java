package com.globalsources.email.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.email.po.EmailModelPO;
import com.globalsources.email.service.SendEmailService;
import com.globalsources.email.utils.SendEmailUtils;
import com.google.common.collect.Maps;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service("CommonTemplateImpl")
public class CommonTemplateImpl implements SendEmailService {
    @Autowired
    private SendEmailUtils sendEmailUtils;

    @Override
    public void sendEmailHtml(String prefix, EmailModelPO emailModelPO) throws MessagingException, IOException, TemplateException {
        if (Objects.nonNull(emailModelPO) && Objects.nonNull(emailModelPO.getTemplateId())) {
            log.info(":::::: common template emailModelPO ---------------:{}", JSON.toJSONString(emailModelPO));
            emailModelPO.setSubject(emailModelPO.getSubject());
            emailModelPO.setTemplateParams(Optional.ofNullable(emailModelPO.getTemplateParams()).orElse(Maps.newHashMap()));
            Map<String, Object> context = new HashMap<>();
            context.put("email",emailModelPO.getTo());
            context.put("content",emailModelPO.getContent());
            int year = LocalDate.now().getYear();
            context.put("year", String.valueOf(year));
            sendEmailUtils.sendEmailTemplate(prefix, "", "hkport25", emailModelPO, context);
        }
    }
}
