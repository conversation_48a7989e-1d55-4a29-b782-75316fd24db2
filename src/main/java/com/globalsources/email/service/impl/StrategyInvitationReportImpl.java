package com.globalsources.email.service.impl;

import com.globalsources.email.enums.EmailSubjectEnum;
import com.globalsources.email.po.EmailModelPO;
import com.globalsources.email.service.SendEmailService;
import com.globalsources.email.utils.SendEmailUtils;
import com.google.common.collect.Maps;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/1/7 11:00
 */
@Service(value = "StrategyInvitationReportImpl")
public class StrategyInvitationReportImpl implements SendEmailService {

    @Value("${ts.invitation.report.person:Charlotte}")
    private String contactPerson;

    @Autowired
    private SendEmailUtils sendEmailUtils;

    @Override
    public void sendEmailHtml(String prefix, EmailModelPO emailModelPO) throws MessagingException, IOException, TemplateException {
        if (Objects.nonNull(emailModelPO) && Objects.nonNull(emailModelPO.getTemplateId())) {
            if (StringUtils.isNotEmpty(emailModelPO.getSubject())) {
                emailModelPO.setSubject(emailModelPO.getSubject());
            } else {
                emailModelPO.setSubject(EmailSubjectEnum.match(EmailSubjectEnum.INVITATION_DAILY_REPORT.getKey()));
            }
            emailModelPO.setTemplateParams(Optional.ofNullable(emailModelPO.getTemplateParams()).orElse(Maps.newHashMap()));
            String year = String.valueOf(Optional.ofNullable(emailModelPO.getTemplateParams().get("year")).orElse(LocalDate.now().getYear()));
            String season = String.valueOf(Optional.ofNullable(emailModelPO.getTemplateParams().get("season")).orElse(Strings.EMPTY));

            Map<String, Object> context = new HashMap<>(16);
            context.put("year", year);
            context.put("season", season);
            context.put("person", contactPerson);
            sendEmailUtils.sendEmailTemplate(prefix, "", "hkport25", emailModelPO, context);
        }
    }
}
