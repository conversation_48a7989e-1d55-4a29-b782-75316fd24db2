package com.globalsources.eblock.agg.api.model.dto.audit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ReviewSettingListQueryDTO")
public class ReviewSettingListQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("审核类型：Send, Block, Blacklist, Resend")
    private String action;
}
