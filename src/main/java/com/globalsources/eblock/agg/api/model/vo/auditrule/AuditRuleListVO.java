package com.globalsources.eblock.agg.api.model.vo.auditrule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AuditRuleListVO", description = "Rule列表")
public class AuditRuleListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("Rule id")
    private String ruleId;

    @ApiModelProperty("Rule Name:流程名称")
    private String ruleName;

    @ApiModelProperty("Action")
    private String ruleAction;

    @ApiModelProperty("Description:流程简介")
    private String ruleDesc;

    @ApiModelProperty("Rule Status")
    private Boolean status;

    @ApiModelProperty("Updated Time")
    private Date lUpdDate;

    @ApiModelProperty("Updated By")
    private Long editBy;

    @ApiModelProperty("Updated Email")
    private String editEmailAddr;

    @ApiModelProperty("Trigger Times")
    private Long triggerCount;

    @ApiModelProperty("预测状态,true=预测中,按钮不可点击")
    private boolean forecastFlag;
}
