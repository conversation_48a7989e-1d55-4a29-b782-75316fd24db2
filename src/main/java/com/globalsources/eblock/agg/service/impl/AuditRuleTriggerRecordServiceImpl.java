package com.globalsources.eblock.agg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.eblock.agg.api.model.dto.auditrule.AuditRuleTriggerTimesDTO;
import com.globalsources.eblock.agg.dao.AuditRuleResultRecordMapper;
import com.globalsources.eblock.agg.dao.AuditRuleTriggerRecordMapper;
import com.globalsources.eblock.agg.entity.AuditRuleTriggerRecordPO;
import com.globalsources.eblock.agg.service.AuditRuleTriggerRecordService;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 规则命中记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Slf4j
@Service
public class AuditRuleTriggerRecordServiceImpl extends ServiceImpl<AuditRuleTriggerRecordMapper, AuditRuleTriggerRecordPO> implements AuditRuleTriggerRecordService {

    @Autowired
    private AuditRuleTriggerRecordMapper auditRuleTriggerRecordMapper;

    @Autowired
    private AuditRuleResultRecordMapper auditRuleResultRecordMapper;

    @Override
    public void saveAuditRuleTriggerRecord(AuditRuleTriggerRecordPO recordPO) {
        log.info("------ saveAuditRuleTriggerRecord, recordPO:{}", recordPO);
        if (Objects.nonNull(recordPO)) {
            try {
                this.save(recordPO);
            } catch (Exception e) {
                log.error("saveAuditRuleTriggerRecord fail", e);
            }
        }
    }

    @Override
    public Integer countAuditRuleTriggerTimes(AuditRuleTriggerTimesDTO dto){
        log.info("------ countAuditRuleTriggerTimes dto:{}", dto);
        if(Objects.isNull(dto) || StringUtils.isBlank(dto.getRuleId()) || StringUtils.isBlank(dto.getStartDate()) || StringUtils.isBlank(dto.getEndDate()) ){
            throw new BusinessException(ResultCodeEnum.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }
        Integer count = auditRuleResultRecordMapper.countAuditRuleTriggerTimes(dto);
        log.info("------ countAuditRuleTriggerTimes count:{}", count);
        return count;
    }

    @Override
    public Integer forecastResultCount(String uuid, String type,String ruleUuid) {
        return this.baseMapper.forecastResultCount(uuid,type,ruleUuid);
    }

}
