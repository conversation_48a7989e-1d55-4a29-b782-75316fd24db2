package com.globalsources.eblock.agg.job;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.globalsources.agg.supplier.api.feign.SupplierSeoAggFeign;
import com.globalsources.agg.supplier.api.model.dto.request.seo.SuppSeoInfoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.seo.SuppSeoInfoAggDTO;
import com.globalsources.eblock.agg.entity.AuditInfoPO;
import com.globalsources.eblock.agg.service.impl.AuditInfoServiceImpl;
import com.globalsources.eblock.agg.utils.GetIpUtil;
import com.globalsources.eblock.agg.utils.StringUtil;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.product.agg.api.feign.BuyerProductFeign;
import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.rfi.agg.core.dto.match.InquiryMatchResultDTO;
import com.globalsources.rfi.agg.core.vo.record.InquiryThirdRecordVO;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.agg.enums.ReviewTypeEnum;
import com.globalsources.rfi.agg.feign.BuyerInquiryFeign;
import com.globalsources.rfi.agg.feign.InquiryFeign;
import com.globalsources.rfi.agg.feign.InquiryMatchResultFeign;
import com.globalsources.rfi.agg.feign.InquiryThirdRecordFeign;
import com.globalsources.rfx.service.IDictService;
import com.globalsources.user.api.feign.UserProfileFeign;
import com.globalsources.user.api.feign.UserQueryFeign;
import com.globalsources.user.api.vo.BaseUserInfoVO;
import com.globalsources.user.api.vo.ProfileInfoVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FillDataHandler {

    @Autowired
    private AuditInfoServiceImpl auditInfoService;
    @Autowired
    private BuyerProductFeign productFeign;
    @Autowired
    private InquiryFeign inquiryFeign;
    @Autowired
    private InquiryMatchResultFeign inquiryMatchResultFeign;
    @Autowired
    private BuyerInquiryFeign buyerInquiryFeign;
    @Autowired
    private UserProfileFeign userProfileFeign;
    @Autowired
    private UserQueryFeign userQueryFeign;
    @Autowired
    private IDictService dictService;
    @Autowired
    private SupplierSeoAggFeign supplierSeoAggFeign;
    @Autowired
    private InquiryThirdRecordFeign inquiryThirdRecordFeign;

    private static final String DEFAULT_BUYER_IP = "127.0.0.1";

    private Map<Long, SuppSeoInfoAggDTO> supplierInfoCacheMap = new HashMap<>(20000);

    private Map<String, String> countryNameCacheMap = new HashMap<>(200);

    private Map<Long, ProfileInfoVO> buyerInfoCacheMap = new HashMap<>(150000);

    private Map<Long, BaseUserInfoVO> baseBuyerInfoCacheMap = new HashMap<>(150000);

    /**
     * 补充audit_info数据
     */
    @XxlJob("fillAuditInfoData")
    public void fillAuditInfoData() {
        log.info("fillAuditInfoData job start");
        String paramStr = XxlJobHelper.getJobParam();
        String[] params = paramStr.split(",");

        String tableName = "audit_info";
        if (StringUtils.isNotEmpty(params[0])) {
            tableName = params[0];
        }

        try {
            // 查询总数量
            Long totalCount = auditInfoService.selectAuditInfoCount(tableName);

            int limitNum = Integer.parseInt(params[1]);
            int offset = 0;
            String requestId = null;
            if (StringUtils.isNotEmpty(params[2])) {
                requestId = String.valueOf(params[2]);
            }
            List<AuditInfoPO> auditInfoList = auditInfoService.selectAuditInfo(tableName, limitNum, requestId);
            while (CollectionUtils.isNotEmpty(auditInfoList)) {
                log.info("fillAuditInfoData limit:{}, offset:{},total:{},requestId:{}", limitNum, offset, totalCount, requestId);
                // 逻辑处理
                fillAuditInfo(auditInfoList);
                // 更新
                try {
                    auditInfoService.updateBatch(tableName, auditInfoList);
                } catch (Exception e) {
                    log.error("update batch error :{}", JSON.toJSONString(e));
                }
                requestId = auditInfoList.get(auditInfoList.size() - 1).getRequestId();

                offset += auditInfoList.size();
                auditInfoList = auditInfoService.selectAuditInfo(tableName, limitNum, requestId);
            }
            supplierInfoCacheMap.clear();
            countryNameCacheMap.clear();
            buyerInfoCacheMap.clear();
            baseBuyerInfoCacheMap.clear();
            log.info("fillAuditInfoData job end");
        } catch (Exception e) {
            log.error("fillAuditInfoData error ", e);
        }
    }


    private void fillAuditInfo(List<AuditInfoPO> auditInfoList) {
        // 批量查询产品信息
        List<Long> productIds = auditInfoList.stream().map(AuditInfoPO::getProductId).distinct().collect(Collectors.toList());
        List<ProductLiteVO> productLiteVOList = productFeign.getLiteProductListByIds(productIds, false, false, false).getData();
        Map<Long, ProductLiteVO> productMap = productLiteVOList.stream().collect(Collectors.toMap(ProductLiteVO::getProductId, x -> x, (oldValue, newValue) -> newValue));
        // 批量查询用户基础信息
        batchBaseBuyerProfile(auditInfoList);
        auditInfoList.forEach(auditInfoPO -> {
            if (Objects.isNull(auditInfoPO.getBuyerSendIp())) {
                auditInfoPO.setBuyerSendIp(DEFAULT_BUYER_IP);
            }
            String filterSubject = StringUtil.filter(auditInfoPO.getSubject());
            auditInfoPO.setSubject(filterSubject);
            // tmx
            setTmxInfo(auditInfoPO);
            // 设置产品信息
            setProductInfo(auditInfoPO, productMap);
            // 设置供应商信息
            setSupplierInfo(auditInfoPO);
            // 买家信息
            setBuyerInfo(auditInfoPO);
        });
    }

    private void setTmxInfo(AuditInfoPO auditInfoPO) {
        InquiryThirdRecordVO inquiryThirdRecordVO = inquiryThirdRecordFeign.getRfiRequest(auditInfoPO.getRequestId(), ReviewTypeEnum.TMX.getKey()).getData();
        if (Objects.nonNull(inquiryThirdRecordVO)) {
            try {
                String respData = inquiryThirdRecordVO.getRespData();
                respData = URLDecoder.decode(respData, "UTF-8");
                if (StringUtils.isNotEmpty(respData)) {
                    String[] aArray = respData.split(",");
                    String policyScore = "";
                    String riskRating = "";
                    for (String c : aArray) {
                        if (c.contains("\"policy_score\"")) {
                            policyScore = c.replace("policy_score", "").replace("\"", "").replace(":", "");
                            break;
                        }
                    }
                    for (String c : aArray) {
                        if (c.contains("\"risk_rating\"")) {
                            riskRating = c.replace("risk_rating", "").replace("\"", "").replace(":", "");
                            break;
                        }
                    }
                    policyScore = URLDecoder.decode(policyScore, "UTF-8");
                    if (StringUtils.isNotEmpty(policyScore)) {
                        auditInfoPO.setTmxPolicyScore(Integer.parseInt(policyScore));
                    }
                    if (StringUtils.isNotEmpty(riskRating)) {
                        auditInfoPO.setTmxRiskRating(riskRating);
                    }
                }
            } catch (UnsupportedEncodingException e) {
                log.error("UnsupportedEncodingException, inquiry id:{}", auditInfoPO.getRequestId());
            }
        }
    }

    private void setProductInfo(AuditInfoPO auditInfoPO, Map<Long, ProductLiteVO> productMap) {
        ProductLiteVO productLiteVO = productMap.get(auditInfoPO.getProductId());
        if (Objects.nonNull(productLiteVO)) {
            auditInfoPO.setCategoryId(productLiteVO.getCategoryId());
            auditInfoPO.setProductName(productLiteVO.getProductName());
            auditInfoPO.setCategoryName(productLiteVO.getCategoryName());
            auditInfoPO.setModelNumber(productLiteVO.getModelNumber());
        }
    }

    private void setSupplierInfo(AuditInfoPO auditInfoPO) {
        List<Long> supplierIdList = getSupplierIdList(auditInfoPO);
        auditInfoPO.setSupplierIdList(Strings.join(supplierIdList, ','));
        auditInfoPO.setSupplierCnt(supplierIdList.size());
        SuppSeoInfoAggDTO supplierInfo = getSupplierInfo(auditInfoPO.getSupplierId());
        if (Objects.nonNull(supplierInfo) && Objects.nonNull(supplierInfo.getSupplierId())) {
            auditInfoPO.setSupplierName(supplierInfo.getCompanyName());
        }
    }

    private void setBuyerInfo(AuditInfoPO auditInfoPO) {
        BaseUserInfoVO baseUserInfoVO = baseBuyerInfoCacheMap.get(auditInfoPO.getBuyerId());
        if (Objects.nonNull(baseUserInfoVO)) {
            auditInfoPO.setBuyerBusinessType(baseUserInfoVO.getBusinessType());
            auditInfoPO.setBuyerRegisterDate(baseUserInfoVO.getCreateDate());
        }
        ProfileInfoVO userBaseProfileVO = getBuyerProfile(auditInfoPO.getBuyerId());
        if (Objects.nonNull(userBaseProfileVO) && Objects.nonNull(userBaseProfileVO.getContactInfo())) {
            auditInfoPO.setBuyerEmailAddr(userBaseProfileVO.getContactInfo().getEmail());
            auditInfoPO.setBuyerFirstName(userBaseProfileVO.getContactInfo().getFirstName());
            auditInfoPO.setBuyerLastName(userBaseProfileVO.getContactInfo().getLastName());
            auditInfoPO.setBuyerCompanyWebsite(userBaseProfileVO.getContactInfo().getCompanyUrl());
            auditInfoPO.setBuyerCountryCode(userBaseProfileVO.getContactInfo().getCountryCode());
            auditInfoPO.setBuyerCountryName(getCountryNameByCode(userBaseProfileVO.getContactInfo().getCountryCode()));
            auditInfoPO.setBuyerTitle(userBaseProfileVO.getContactInfo().getTitle());
            auditInfoPO.setBuyerJobTitle(userBaseProfileVO.getContactInfo().getJobTitle());
            auditInfoPO.setBuyerProvince(userBaseProfileVO.getContactInfo().getProvince());
            auditInfoPO.setBuyerCity(userBaseProfileVO.getContactInfo().getCity());
            auditInfoPO.setBuyerZipCode(userBaseProfileVO.getContactInfo().getZipCode());
            auditInfoPO.setBuyerTelCountryCode(userBaseProfileVO.getContactInfo().getTelCountryCode());
            auditInfoPO.setBuyerTelArea(userBaseProfileVO.getContactInfo().getTelAreaCode());
            auditInfoPO.setBuyerTelNum(userBaseProfileVO.getContactInfo().getPhone());
            auditInfoPO.setBuyerTelExt(userBaseProfileVO.getContactInfo().getTelExt());
            auditInfoPO.setBuyerLinkedlnUrl(userBaseProfileVO.getContactInfo().getLinkedInUrl());
            auditInfoPO.setBuyerJobFunction(userBaseProfileVO.getContactInfo().getJobFunction());
            auditInfoPO.setBuyerWechatId(userBaseProfileVO.getContactInfo().getWechatAccount());
        }
        if (Objects.nonNull(userBaseProfileVO) && Objects.nonNull(userBaseProfileVO.getCompanyInfo())) {
            auditInfoPO.setBuyerCompanyName(userBaseProfileVO.getCompanyInfo().getCompanyName());
        }
        auditInfoPO.setBuyerIpCountryCode(GetIpUtil.getCountryCode(auditInfoPO.getBuyerSendIp()));
    }

    private List<Long> getSupplierIdList(AuditInfoPO auditInfoPO) {
        if (InquiryTypeEnum.CATEGORY.getKey().equals(auditInfoPO.getInquiryType())) {
            // 类别询盘
            InquiryMatchResultDTO inquiryMatchResultDTO = inquiryMatchResultFeign.getMatchResultInquiryId(auditInfoPO.getRequestId()).getData();
            if (ObjectUtils.isEmpty(inquiryMatchResultDTO)) {
                return Collections.singletonList(auditInfoPO.getSupplierId());
            } else {
                //格式化json匹配规则
                String json = JSON.parse(inquiryMatchResultDTO.getMatchResultJson().toString()).toString();
                List<Long> productIdList = JSON.parseArray(json, Long.class);
                List<Long> filterResult = new ArrayList<>(productIdList.size());
                List<ProductLiteVO> productList = productFeign.getLiteProductListByIds(productIdList, false, false, false).getData();
                if (CollectionUtils.isNotEmpty(productList)) {
                    filterResult.addAll(productList.stream().filter(Objects::nonNull).map(ProductLiteVO::getSupplierId).distinct().collect(Collectors.toList()));
                }
                filterResult.add(auditInfoPO.getSupplierId());
                return filterResult;
            }
        } else {
            // 产品询盘 or 供应商询盘
            return Collections.singletonList(auditInfoPO.getSupplierId());
        }
    }

    private void batchBaseBuyerProfile(List<AuditInfoPO> auditInfoList) {
        List<Long> buyerIds = CollUtil.newArrayList();
        auditInfoList.forEach(auditInfoPO -> {
            if (!baseBuyerInfoCacheMap.containsKey(auditInfoPO.getBuyerId())) {
                buyerIds.add(auditInfoPO.getBuyerId());
            }
        });
        // 批量查询用户基础信息
        if (CollUtil.isNotEmpty(buyerIds)) {
            List<BaseUserInfoVO> userInfoVOList = userQueryFeign.queryBaseUsers(buyerIds).getData();
            baseBuyerInfoCacheMap.putAll(userInfoVOList.stream().collect(Collectors.toMap(BaseUserInfoVO::getUserId, x -> x, (oldValue, newValue) -> newValue)));
        }
    }

    private ProfileInfoVO getBuyerProfile(Long buyerId) {
        if (Objects.isNull(buyerId)) {
            return null;
        }
        if (buyerInfoCacheMap.containsKey(buyerId)) {
            return buyerInfoCacheMap.get(buyerId);
        }
        ProfileInfoVO userBaseProfileVO = userProfileFeign.getProfileInfo(buyerId, true).getData();
        buyerInfoCacheMap.put(buyerId, userBaseProfileVO);
        return userBaseProfileVO;
    }

    private String getCountryNameByCode(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        if (countryNameCacheMap.containsKey(countryCode)) {
            return countryNameCacheMap.get(countryCode);
        }
        String countryName = dictService.getCountryNameByCode(countryCode, LanguageDicEnum.EN_US.getValue());
        countryNameCacheMap.put(countryCode, countryName);
        return countryName;
    }

    private SuppSeoInfoAggDTO getSupplierInfo(Long supplierId) {
        if (Objects.isNull(supplierId)) {
            return null;
        }
        if (supplierInfoCacheMap.containsKey(supplierId)) {
            return supplierInfoCacheMap.get(supplierId);
        }
        SuppSeoInfoQueryAggDTO suppSeoInfoQueryAggDTO = new SuppSeoInfoQueryAggDTO();
        suppSeoInfoQueryAggDTO.setSupplierIds(CollUtil.newArrayList(supplierId));
        List<SuppSeoInfoAggDTO> suppSeoInfoAggDTOS = supplierSeoAggFeign.getSuppSeoInfoDtoBatch(suppSeoInfoQueryAggDTO).getData();
        if (CollUtil.isNotEmpty(suppSeoInfoAggDTOS)) {
            supplierInfoCacheMap.put(supplierId, suppSeoInfoAggDTOS.get(0));
            return suppSeoInfoAggDTOS.get(0);
        }
        return null;
    }
}
