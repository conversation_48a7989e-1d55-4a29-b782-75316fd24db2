package com.globalsources.eblock.agg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-12-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "audit_rule")
public class AuditRulePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    /**
     * rule_id
     */
    private String ruleId;

    /**
     * rule_name
     */
    private String ruleName;

    /**
     * rule_desc
     */
    private String ruleDesc;

    /**
     * rule_action
     */
    private String ruleAction;

    /**
     * el_data
     */
    private String elData;

    /**
     * rule_data
     */
    private String ruleData;

    /**
     * create_date
     */
    private Date createDate;

    /**
     * l_upd_date
     */
    private Date lUpdDate;

    /**
     * application_name
     */
    private String applicationName;

    /**
     * draft_status
     */
    private String draftStatus;

    /**
     * status
     */
    private Boolean status;

    /**
     * delete_flag
     */
    private Boolean deleteFlag;

    /**
     * edit_by
     */
    private Long editBy;

    /**
     * create_by
     */
    private Long createBy;

    /**
     * master_rule_flag
     */
    private Boolean masterRuleFlag;

    /**
     * trigger_count
     */
    private Long triggerCount;

}