package com.globalsources.ts.model.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: JiangsuRecommendProduct </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2022/10/20-14:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JiangsuRecommendProduct implements Serializable {

    private Long productId;

    private Integer rank;
}
