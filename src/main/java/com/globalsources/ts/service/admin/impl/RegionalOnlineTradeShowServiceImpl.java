package com.globalsources.ts.service.admin.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.framework.result.PageResult;
import com.globalsources.ts.api.model.vo.desktop.HomeOnlineShowVO;
import com.globalsources.ts.api.model.vo.desktop.OnlineRecommendProductVO;
import com.globalsources.ts.api.model.vo.desktop.RegionalOnlineShowVO;
import com.globalsources.ts.dao.admin.IRegionalOnlineTradeShowDao;
import com.globalsources.ts.entity.admin.RegionalOnlineTradeShowEntity;
import com.globalsources.ts.service.admin.RegionalOnlineShowSupplierService;
import com.globalsources.ts.service.admin.RegionalOnlineTradeShowService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Service
@DS("pg-admin")
@AllArgsConstructor
public class RegionalOnlineTradeShowServiceImpl extends ServiceImpl<IRegionalOnlineTradeShowDao, RegionalOnlineTradeShowEntity> implements RegionalOnlineTradeShowService {

    private final IRegionalOnlineTradeShowDao regionalOnlineTradeShowDao;
    private final RegionalOnlineShowSupplierService regionalOnlineShowSupplierService;

    @Override
    public RegionalOnlineShowVO getRegionalOnlineShow(String regionName, String exhibitType) {
        return regionalOnlineTradeShowDao.getRegionalOnlineShow(regionName, exhibitType);
    }

    @Override
    public PageResult<OnlineRecommendProductVO> getTsRecommendedProdByTsKey(String regionName, Long pageSize, Long pageNum) {
        return regionalOnlineShowSupplierService.getRecommendedProdByTsKey(regionName, pageSize, pageNum);
    }

    @Override
    public HomeOnlineShowVO getTradeShowList(String regionName) {
        return regionalOnlineTradeShowDao.getTradeShowList(regionName);
    }
}
