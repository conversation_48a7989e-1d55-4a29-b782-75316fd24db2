package com.globalsources.ts.service.admin;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.ts.api.model.dto.desktop.PastConferenceVideoDTO;
import com.globalsources.ts.api.model.vo.desktop.PastConferenceVideoVO;

import java.util.List;

public interface PastConferenceVideoService {
    Result<PageResult<PastConferenceVideoVO>> queryList(Long pageNum, Long pageSize);

    Result<PastConferenceVideoVO> getLatestModifyOne();

    Result<Void> modifySortNumOfConferenceVideo(Long currentId, String direction);

    Result<Void> delete(Long userId,Long spcId);

    Result<PastConferenceVideoVO> getInfo(Long spcId);

    Result<Void> edit(Long userId,PastConferenceVideoDTO dto);

    Result<Void> add(Long userId,PastConferenceVideoDTO dto);

    Result<List<PastConferenceVideoVO>> queryConferenceVideoList(Long limit);
}
