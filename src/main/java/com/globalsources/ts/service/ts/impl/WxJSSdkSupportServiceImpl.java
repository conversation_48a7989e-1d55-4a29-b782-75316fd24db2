package com.globalsources.ts.service.ts.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.HttpUtil;
import com.globalsources.ts.service.ts.WxJSSdkSupportService;
import com.globalsources.ts.vo.WxJsSdkSupportVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Formatter;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class WxJSSdkSupportServiceImpl implements WxJSSdkSupportService {


    public static final String ACCESS_TOKEN_PARAM_KEY = "access_token";

    public static final String JS_TICKET_PARAM_KEY = "ticket";

    public static final String ACCESS_TOKEN_CACHE_KEY = "WX_WEBSITE_ACCESS_TOKEN";

    public static final String JS_TICKET_CACHE_KEY = "WX_WEBSITE_JS_API_TICKET";


    @Value("${wx.website.application.appId}")
    private String appId;

    @Value("${wx.website.application.appSecret}")
    private String appSecret;

    @Value("${wx.website.application.access_token_url:https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s}")
    private String accessTokenUrl;

    @Value("${wx.website.application.js_api_ticket_url:https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi}")
    private String jsApiTicketUrl;


    private final RedisTemplate<String, String> redisTemplate;


    @Override
    public String getWxAccessToken() {
        // 优先从缓存中获取
        if (Optional.ofNullable(redisTemplate.getExpire(ACCESS_TOKEN_CACHE_KEY)).orElse(0L) >= 10) {
            return redisTemplate.opsForValue().get(ACCESS_TOKEN_CACHE_KEY);
        }
        // 若缓存中没有或10s内到期则重新获取
        String getAccessTokenUrl = String.format(accessTokenUrl, appId, appSecret);
        return getAndCacheWxParam(ACCESS_TOKEN_PARAM_KEY, ACCESS_TOKEN_CACHE_KEY, getAccessTokenUrl);
    }


    private String getAndCacheWxParam(String paramKey, String cacheKey, String queryUrl) {

        String result = null;

        try {
            result = HttpUtil.get(queryUrl);
        } catch (IOException e) {
            String errorMsg = String.format("=====>>>>> during get wx %s occurred exception, url:%s", paramKey, queryUrl);
            log.error(errorMsg, e);
        }

        JSONObject jsonObject = JSON.parseObject(result);
        String ticket = jsonObject.getString(paramKey);
        Integer expires = jsonObject.getInteger("expires_in");

        if (StringUtils.isEmpty(ticket) || Objects.isNull(expires)) {
            log.warn("=====>>>>> get wx {} return null, queryUrl:{}, response:{}", paramKey, queryUrl, result);
            throw new BusinessException(ResultCode.CommonResultCode.FAILED);
        }

        // 放入缓存 统一获取并设置过期时间
        redisTemplate.opsForValue().set(cacheKey, ticket, expires, TimeUnit.SECONDS);

        return ticket;
    }


    @Override
    public String getWxJsApiTicket() {
        // 优先从缓存中获取
        if (Optional.ofNullable(redisTemplate.getExpire(JS_TICKET_CACHE_KEY)).orElse(0L) >= 10) {
            return redisTemplate.opsForValue().get(JS_TICKET_CACHE_KEY);
        }

        // 若缓存中没有或10s内到期则重新获取
        String wxAccessToken = getWxAccessToken();
        if (StringUtils.isEmpty(wxAccessToken)) {
            throw new BusinessException(ResultCode.CommonResultCode.FAILED);
        }
        String getJsApiTicketUrl = String.format(jsApiTicketUrl, wxAccessToken);

        return getAndCacheWxParam(JS_TICKET_PARAM_KEY, JS_TICKET_CACHE_KEY, getJsApiTicketUrl);
    }


    @Override
    public Result<WxJsSdkSupportVO> getWxJsSdkSupportParams(String url) {

        String jsApiTicket = getWxJsApiTicket();
        String nonceStr = UUID.randomUUID().toString();
        long timestamp = System.currentTimeMillis() / 1000;

        String assembleStr = "jsapi_ticket=" + jsApiTicket +
                "&noncestr=" + nonceStr +
                "&timestamp=" + timestamp +
                "&url=" + url;

        String signature = null;

        try {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(assembleStr.getBytes(StandardCharsets.UTF_8));
            signature = byteToHex(crypt.digest());
        } catch (Exception e) {
            log.error("=====>>>>> encrypt get wx js api signature params occurred exception", e);
        }

        if (StringUtils.isEmpty(signature)) {
            log.warn("=====>>>>> get wx js api signature return null, assembleStr:{}", assembleStr);
            throw new BusinessException(ResultCode.CommonResultCode.FAILED);
        }

        WxJsSdkSupportVO wxJsSdkSupportVO = new WxJsSdkSupportVO()
                .setAppId(appId)
                .setTimestamp(timestamp)
                .setNonceStr(nonceStr)
                .setSignature(signature);

        return Result.success(wxJsSdkSupportVO);
    }


    private String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash) {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }

}
