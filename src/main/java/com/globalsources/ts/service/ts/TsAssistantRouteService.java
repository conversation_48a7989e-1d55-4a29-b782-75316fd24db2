package com.globalsources.ts.service.ts;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.ts.api.model.dto.app.TsAssistantRouteAddDTO;
import com.globalsources.ts.api.model.dto.app.TsAssistantRouteDTO;
import com.globalsources.ts.api.model.dto.app.TsAssistantRouteDetailDTO;
import com.globalsources.ts.api.model.vo.app.TsAssistantRouteDetailVO;
import com.globalsources.ts.api.model.vo.app.TsAssistantRouteVO;
import com.globalsources.ts.entity.ts.TsAssistantRouteEntity;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface TsAssistantRouteService extends IService<TsAssistantRouteEntity> {

    List<TsAssistantRouteVO> getRouteList(TsAssistantRouteDTO dto);

    Boolean addRoute(TsAssistantRouteAddDTO dto);

    Boolean deleteRoute(TsAssistantRouteDetailDTO dto);

    TsAssistantRouteDetailVO routeDetail(TsAssistantRouteDetailDTO dto);

    Boolean hadRoute(TsAssistantRouteDTO dto);
}
