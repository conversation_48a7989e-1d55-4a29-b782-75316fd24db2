package com.globalsources.ts.api.model.dto.desktop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TsCampaignCreateDTO implements Serializable {

    private Long campaignId;

    @ApiModelProperty("Campaign Name")
    @NotNull
    private String campaignName;

    @ApiModelProperty("Campaign Type")
    @NotNull
    private String campaignType;

    @ApiModelProperty("活动展示时间")
    @NotNull
    private Long campaignDisplayStartDate;

    @NotNull
    @ApiModelProperty("活动展示时间")
    private Long campaignDisplayEndDate;

    @NotNull
    @ApiModelProperty("活动有效期")
    private Long campaignStartDate;

    @NotNull
    @ApiModelProperty("活动有效期")
    private Long campaignEndDate;

    @ApiModelProperty("奖品兑换码")
    private String giftRedeemCode;

    @ApiModelProperty("礼品数量")
    private Integer giftQuantity;

    private Long createBy;

    private Long lUpdBy;

    private String topBannerKey;

    private String bottomBannerKey;

    private String campaignMapKey;

    private String instructionsKey;

    private String termsAndConditions;
}
