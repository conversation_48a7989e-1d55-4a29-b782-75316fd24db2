package com.globalsources.ts.api.model.dto.desktop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShowSectionCollectSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @ApiModelProperty("id")
    private Long showSectionId;

    @NotBlank
    @Size(max = 30, message = "sectionName max length is 30")
    @ApiModelProperty("Menu: 版块名称")
    private String sectionName;

    @NotNull
    @ApiModelProperty("Status: 状态")
    private Boolean status;

    @Size(max = 200, message = "info max length is 200")
    @ApiModelProperty("Description: 描述")
    private String info;

    @NotBlank
    @ApiModelProperty("Content： 内容")
    private String content;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private String emailAddr;
}
