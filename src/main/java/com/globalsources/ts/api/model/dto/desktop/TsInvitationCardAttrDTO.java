package com.globalsources.ts.api.model.dto.desktop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TsInvitationCardAttrDTO implements Serializable {

    @ApiModelProperty(value = "file key")
    private String fileKey;

    @ApiModelProperty(value = "content")
    private String content;

}
