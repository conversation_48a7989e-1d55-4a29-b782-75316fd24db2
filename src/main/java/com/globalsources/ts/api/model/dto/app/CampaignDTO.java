package com.globalsources.ts.api.model.dto.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampaignDTO {

    @ApiModelProperty(value = "活动类型，唯一")
    private String campaignType;
    @ApiModelProperty(value = "任务用户")
    private String taskUser;
}
