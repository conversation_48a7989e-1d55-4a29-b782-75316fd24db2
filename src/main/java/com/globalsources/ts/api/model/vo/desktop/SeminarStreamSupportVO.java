package com.globalsources.ts.api.model.vo.desktop;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * seminar stream support
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeminarStreamSupportVO implements Serializable {

    /**
     * primary key
     */
    private Integer streamSupportId;

    /**
     * stream support logo url, AS a key  
     */
    private String streamSupportLogo;

    /**
     * seminar support detail url, of the logo 
     */
    private String streamSupportUrl;

}
