package com.globalsources.ts.h5.controller;

import com.globalsources.framework.result.Result;
import com.globalsources.ts.api.feign.IndonesianTsFeign;
import com.globalsources.ts.api.model.vo.SeasonProductPrefVO;
import com.globalsources.ts.api.model.vo.TsInfoVO;
import com.globalsources.ts.api.model.vo.TsProductPrefVO;
import com.globalsources.ts.util.HttpHeadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <a>Title: IndonesianController </a>
 * <a>Author: Levlin Li <a>
 * <a>Description：<a>
 *
 * <AUTHOR> Li
 * @date 2023/4/5-16:12
 */
@Slf4j
@RefreshScope
@RestController
@Api(tags = {"印尼在线展-H5"})
@RequiredArgsConstructor
@RequestMapping("/h5/indonesian")
public class IndonesianH5Controller {

    private final IndonesianTsFeign indonesianTsFeign;

    @ApiOperation("获取印尼展会列表")
    @GetMapping("/v1/season-list")
    public Result<List<TsInfoVO>> getSeasonList(@RequestParam(required = false) String lang, HttpServletRequest request) {
        lang = StringUtils.isBlank(lang) ? HttpHeadUtil.getLang(request) : lang;
        log.info("------ id getSeasonList lang:{}", lang);
        return indonesianTsFeign.getSeasonList(lang);
    }

    @ApiOperation("获取印尼展会productPref")
    @GetMapping("/v1/product/pref/list")
    public Result<List<SeasonProductPrefVO>> queryProductPref(@RequestParam(required = false) String lang, HttpServletRequest request) {
        lang = StringUtils.isBlank(lang) ? HttpHeadUtil.getLang(request) : lang;
        log.info("------ id getSeasonList lang:{}", lang);
        Result<List<TsProductPrefVO>> listResult = indonesianTsFeign.getProductPrefList(lang);
        if (listResult.getData() != null) {
            List<SeasonProductPrefVO> productPrefVOList = new ArrayList<>();
            Map<String, List<TsProductPrefVO>> collect = listResult.getData().stream().collect(Collectors.groupingBy(TsProductPrefVO::getTsGroupCode));
            for (Map.Entry<String, List<TsProductPrefVO>> entry : collect.entrySet()) {
                productPrefVOList.add(SeasonProductPrefVO.builder().tsGroupCode(entry.getKey()).productPreList(entry.getValue()).build());
            }
            return Result.success(productPrefVOList);
        }
        return Result.success();
    }
}
