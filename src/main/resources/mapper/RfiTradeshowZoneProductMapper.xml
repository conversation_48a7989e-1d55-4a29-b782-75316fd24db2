<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.rfi.data.dao.RfiTradeshowZoneProductDao">

    <insert id="batchInsertRfiTradeshowZoneProduct" parameterType="java.util.List">
        INSERT INTO rfi_tradeshow_zone_product(on_site_zone, group_code, product_inquiry_code, product_id, tradeshow_id, create_by, l_upd_by)
        VALUES
        <foreach collection="list" separator="," item="dto">
            (#{dto.onSiteZone}, #{dto.groupCode}, #{dto.productInquiryCode}, #{dto.productId}, #{dto.tradeshowId}, #{dto.createBy}, #{dto.lUpdBy})
        </foreach>
        on conflict
        (on_site_zone, product_id)
        do update set
        (on_site_zone, group_code, product_inquiry_code, product_id, tradeshow_id, create_by, create_date, l_upd_by, l_upd_date, delete_flag)
        = (EXCLUDED.on_site_zone, EXCLUDED.group_code, EXCLUDED.product_inquiry_code, EXCLUDED.product_id, EXCLUDED.tradeshow_id, EXCLUDED.create_by, EXCLUDED.create_date, EXCLUDED.l_upd_by, EXCLUDED.l_upd_date, false)
        where
        rfi_tradeshow_zone_product.delete_flag = true
    </insert>

    <select id="searchRfiTradeshowZoneProductList" resultType="com.globalsources.rfi.agg.response.admin.AdminTradeshowZoneProductVO">
        SELECT
            rfi_tszp_id rfiTszpId,
            on_site_zone onSiteZone,
            group_code groupCode,
            product_inquiry_code productInquiryCode,
            product_id productId
        FROM
            rfi_grp.rfi_tradeshow_zone_product
        WHERE
            delete_flag = FALSE
        <if test="keyword != null and keyword != '' and numFlag == false">
            AND (
            on_site_zone = #{ keyword }
            OR group_code = #{ keyword }
            OR product_inquiry_code = #{ keyword }
            )
        </if>
        <if test="keyword != null and keyword != '' and numFlag == true">
            AND (
            on_site_zone = #{ keyword }
            OR group_code = #{ keyword }
            OR product_inquiry_code = #{ keyword }
            OR product_id = #{ keyword }
            )
        </if>
            ORDER BY
            (case
                when on_site_zone = 'AP' then 0
                when on_site_zone = 'ACS' then 1
                when on_site_zone = 'CS' then 2
                when on_site_zone = 'CZ' then 3
                when on_site_zone = 'CDG' then 4
                when on_site_zone = 'TS' then 5
                when on_site_zone = 'TF' then 6
                else 7
            end),
            group_code,
            product_inquiry_code
    </select>

    <select id="getRfiTradeshowZoneProduct" resultType="com.globalsources.rfi.agg.response.admin.AdminTradeshowZoneProductVO">
        SELECT
            rfi_tszp_id rfiTszpId,
            on_site_zone onSiteZone,
            group_code groupCode,
            product_inquiry_code productInquiryCode,
            product_id productId
        FROM
            rfi_tradeshow_zone_product
        WHERE
            delete_flag = FALSE
        AND on_site_zone = #{onSiteZone}
        AND (
            product_inquiry_code = #{productInquiryCode} OR product_id = #{productId}
        )
        <if test="rfiTszpId != null and rfiTszpId > 0">
            AND rfi_tszp_id != #{rfiTszpId}
        </if>
    </select>

    <select id="getTradeshowZoneProductGroupCodeList" resultType="java.lang.String">
        select
        distinct group_code
        from
        rfi_grp.rfi_tradeshow_zone_product
        where
        delete_flag = false
        and on_site_zone = #{rfiType}
    </select>

    <select id="searchZoneProductList" resultType="com.globalsources.rfi.agg.response.TsOnSiteZoneProductVO">
        SELECT
        rfi_tszp_id rfiTszpId,
        group_code groupCode,
        product_inquiry_code productInquiryCode,
        product_id productId
        FROM
        rfi_grp.rfi_tradeshow_zone_product
        WHERE
        delete_flag = FALSE
        AND on_site_zone = #{onSiteZone}
        ORDER BY
        group_code,
        product_inquiry_code
    </select>
</mapper>
