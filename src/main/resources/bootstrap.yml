spring:
  application:
    name: sensordata-core
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        server-addr: 192.168.117.134:8888
        file-extension: properties
        namespace: d1
        username: develop
        password: 2FrDwJnP
        refresh: true
        extension-configs[0]:
          data-id: common.properties
          group: DEFAULT_GROUP
          refresh: true
      discovery:
        server-addr: 192.168.117.134:8888
        service: sensordata-core
        namespace: d1
        username: develop
        password: 2FrDwJnP