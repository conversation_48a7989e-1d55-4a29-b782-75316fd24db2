//package com.globalsources.search;
//
//import com.globalsources.bff.web.WebBffApp;
//import com.globalsources.framework.result.Result;
//import com.globalsources.user.api.feign.UserSourcingClubActivityFeign;
//import com.globalsources.user.api.vo.UserScoreVo;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = WebBffApp.class)
//public class SourcingScoreTest {
//
//    @Autowired
//    private UserSourcingClubActivityFeign userSourcingClubActivityFeign;
//
//    @Test
//    public void getUserScoreTest(){
//        Result<UserScoreVo> rfiScore = userSourcingClubActivityFeign.getUserScore(1309000087162L, "RFI");
//        Assert.assertNotNull(rfiScore);
//    }
//
//}
