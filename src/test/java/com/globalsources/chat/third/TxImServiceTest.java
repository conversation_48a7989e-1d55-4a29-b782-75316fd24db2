//package com.globalsources.chat.third;
//
//import com.globalsources.chat.ChatApp;
//import com.globalsources.chat.service.UserImService;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
//*
// * <AUTHOR>
// * @since 2022/8/8
//
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ChatApp.class)
//public class TxImServiceTest {
//
//    @Resource
//    private UserImService userImService;
//
//    @Test
//    public void clearChatContact() {
//        userImService.addBlacklist("1309000146189_2008832656220","1309000109090");
//    }
//}
